"use client"

import * as React from "react"
import { 
  Pagination as CommonPagination, 
  PaginationProps as CommonPaginationProps 
} from "@/components/common-custom/pagination"

// ============================================================================
// 项目特定分页组件 - 保持与现有接口的兼容性
// ============================================================================

/**
 * 项目特定的分页组件属性（兼容现有接口）
 */
export interface PaginationProps {
  /**
   * 当前页索引（从0开始）
   */
  pageIndex: number

  /**
   * 总页数
   */
  pageCount: number

  /**
   * 每页显示数量
   */
  pageSize: number

  /**
   * 每页显示数量选项
   * @default [10, 20, 30, 40, 50]
   */
  pageSizeOptions?: number[]

  /**
   * 总记录数
   */
  totalItems: number

  /**
   * 页码变化回调
   * @param page - 新的页索引（从0开始）
   */
  onPageChange: (page: number) => void

  /**
   * 每页显示数量变化回调
   * @param size - 新的每页显示数量
   */
  onPageSizeChange: (size: number) => void

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 通用分页组件的额外配置
   */
  paginationProps?: Omit<CommonPaginationProps, keyof PaginationProps>
}

/**
 * 项目特定的分页组件
 * 
 * 基于通用分页组件，保持与现有项目接口的完全兼容性
 * 同时提供通用组件的所有高级功能
 * 
 * @example
 * ```tsx
 * // 基础用法（与现有代码兼容）
 * <Pagination
 *   pageIndex={currentPage}
 *   pageCount={totalPages}
 *   pageSize={pageSize}
 *   totalItems={totalItems}
 *   onPageChange={setCurrentPage}
 *   onPageSizeChange={setPageSize}
 * />
 * 
 * // 使用高级功能
 * <Pagination
 *   pageIndex={currentPage}
 *   pageCount={totalPages}
 *   pageSize={pageSize}
 *   totalItems={totalItems}
 *   onPageChange={setCurrentPage}
 *   onPageSizeChange={setPageSize}
 *   paginationProps={{
 *     showTotal: true,
 *     showPageSizeChanger: true,
 *     showQuickJumper: true,
 *     disabled: isLoading
 *   }}
 * />
 * ```
 */
export function Pagination({
  pageIndex,
  pageCount,
  pageSize,
  pageSizeOptions = [10, 20, 30, 40, 50],
  totalItems,
  onPageChange,
  onPageSizeChange,
  className,
  paginationProps = {}
}: PaginationProps) {
  // 默认配置，可以通过 paginationProps 覆盖
  const defaultProps: Partial<CommonPaginationProps> = {
    showTotal: true,
    showPageSizeChanger: true,
    showQuickJumper: true,
    disabled: false,
    ...paginationProps
  }

  return (
    <CommonPagination
      pageIndex={pageIndex}
      pageCount={pageCount}
      pageSize={pageSize}
      pageSizeOptions={pageSizeOptions}
      totalItems={totalItems}
      onPageChange={onPageChange}
      onPageSizeChange={onPageSizeChange}
      className={className}
      {...defaultProps}
    />
  )
}

// 导出类型
export type { CommonPaginationProps as ExtendedPaginationProps }
