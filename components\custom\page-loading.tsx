"use client"

import { Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { useNavigation } from "@/components/providers/navigation-provider"
import { useSidebar } from "@/components/navigation/sidebar"
import { useEffect, useState } from "react"
import { usePathname, useSearchParams } from "next/navigation"

/**
 * 页面加载组件
 * 在页面过渡时显示加载指示器
 */
export function PageLoading() {
  const { isPageTransition, shouldShowTransition, visitedPaths } = useNavigation()
  const { state, isMobile } = useSidebar()
  const [mounted, setMounted] = useState(false)
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const currentPath = pathname + searchParams.toString()
  
  // 检查当前路径是否已访问过
  const isCurrentPathVisited = visitedPaths.has(currentPath)
  
  // 解决水合不匹配问题
  useEffect(() => {
    setMounted(true)
  }, [])
  
  // 如果不应该显示过渡效果或没有页面过渡或已经访问过，则不显示
  if (!shouldShowTransition || !isPageTransition || !mounted || isCurrentPathVisited) return null
  
  // 根据侧边栏状态确定左侧偏移量和宽度
  let leftOffset = "left-0"
  let width = "w-full"
  
  if (!isMobile) {
    if (state === "expanded") {
      leftOffset = "left-[var(--sidebar-width)]"
      width = "w-[calc(100%-var(--sidebar-width))]"
    } else {
      leftOffset = "left-[var(--sidebar-width-icon)]"
      width = "w-[calc(100%-var(--sidebar-width-icon))]"
    }
  }

  return (
    <div className={cn(
      "fixed inset-y-0 right-0 z-50 flex items-center justify-center pointer-events-none",
      leftOffset,
      width,
      // 添加过渡效果
      "transition-all duration-300 ease-in-out"
    )}>
      {/* 半透明背景 */}
      <div className="absolute inset-0 bg-background/80 backdrop-blur-[2px] transition-opacity animate-in fade-in" />
      
      {/* 加载指示器 */}
      <div className="relative z-10 flex items-center justify-center animate-in fade-in slide-in-from-bottom-4 duration-300">
        <div className="bg-card rounded-lg shadow-lg p-4 flex items-center gap-3">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
          <span className="text-sm font-medium">页面加载中...</span>
        </div>
      </div>
    </div>
  )
} 