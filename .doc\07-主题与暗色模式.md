# 主题与暗色模式

本文档规范了应用的主题系统和暗色模式的实现，确保在不同视觉偏好下提供一致的用户体验。

## 主题系统概述

我们的主题系统基于CSS变量和Tailwind CSS，提供了灵活的主题定制能力和平滑的明暗切换体验。

### 基础架构

主题系统由以下部分组成：

1. **CSS变量定义** - 在全局CSS中定义主题变量
2. **Tailwind配置** - 在Tailwind配置中映射这些变量
3. **主题提供者** - 使用React上下文管理主题状态
4. **主题切换组件** - 允许用户切换明暗模式

## CSS变量定义

我们使用CSS变量定义所有主题相关的颜色和属性，这使得主题切换变得简单高效。

```css
/* globals.css */
:root {
  /* 浅色主题变量 */
  --background: oklch(0.985 0 0);
  --foreground: oklch(0.145 0 0);
  
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.985 0 0);
  
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  
  --radius: 0.625rem;
}

.dark {
  /* 深色主题变量 */
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  
  --destructive: oklch(0.704 0.191 22.216);
  --destructive-foreground: oklch(0.985 0 0);
  
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
}
```

## Tailwind 配置

在Tailwind配置中，我们将CSS变量映射到Tailwind的颜色系统中：

```js
// tailwind.config.js
module.exports = {
  darkMode: ["class"],
  content: [
    "./app/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "var(--border)",
        input: "var(--input)",
        ring: "var(--ring)",
        background: "var(--background)",
        foreground: "var(--foreground)",
        primary: {
          DEFAULT: "var(--primary)",
          foreground: "var(--primary-foreground)",
        },
        secondary: {
          DEFAULT: "var(--secondary)",
          foreground: "var(--secondary-foreground)",
        },
        muted: {
          DEFAULT: "var(--muted)",
          foreground: "var(--muted-foreground)",
        },
        accent: {
          DEFAULT: "var(--accent)",
          foreground: "var(--accent-foreground)",
        },
        destructive: {
          DEFAULT: "var(--destructive)",
          foreground: "var(--destructive-foreground)",
        },
        card: {
          DEFAULT: "var(--card)",
          foreground: "var(--card-foreground)",
        },
        popover: {
          DEFAULT: "var(--popover)",
          foreground: "var(--popover-foreground)",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 0.25rem)",
        sm: "calc(var(--radius) - 0.375rem)",
      },
    },
  },
  plugins: [],
}
```

## 主题提供者实现

主题提供者组件用于在整个应用中管理主题状态：

```tsx
// components/theme-provider.tsx
"use client"

import { createContext, useContext, useEffect, useState } from "react"

type Theme = "light" | "dark" | "system"

interface ThemeProviderProps {
  children: React.ReactNode
  defaultTheme?: Theme
}

interface ThemeProviderState {
  theme: Theme
  setTheme: (theme: Theme) => void
}

const initialState: ThemeProviderState = {
  theme: "system",
  setTheme: () => null,
}

const ThemeProviderContext = createContext<ThemeProviderState>(initialState)

export function ThemeProvider({
  children,
  defaultTheme = "system",
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(defaultTheme)

  useEffect(() => {
    const root = window.document.documentElement
    root.classList.remove("light", "dark")

    if (theme === "system") {
      const systemTheme = window.matchMedia("(prefers-color-scheme: dark)").matches
        ? "dark"
        : "light"
      root.classList.add(systemTheme)
      return
    }

    root.classList.add(theme)
  }, [theme])

  // 监听系统主题变化
  useEffect(() => {
    if (theme !== "system") return
    
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)")
    
    const handleChange = () => {
      const root = window.document.documentElement
      root.classList.remove("light", "dark")
      root.classList.add(mediaQuery.matches ? "dark" : "light")
    }
    
    mediaQuery.addEventListener("change", handleChange)
    return () => mediaQuery.removeEventListener("change", handleChange)
  }, [theme])

  const value = {
    theme,
    setTheme: (theme: Theme) => {
      setTheme(theme)
      try {
        localStorage.setItem("theme", theme)
      } catch (e) {
        console.error(e)
      }
    },
  }

  return (
    <ThemeProviderContext.Provider value={value}>
      {children}
    </ThemeProviderContext.Provider>
  )
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext)
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider")
  }
  return context
}
```

## 主题切换组件

主题切换组件允许用户在明暗模式之间切换：

```tsx
// components/theme-toggle.tsx
"use client"

import { useTheme } from "@/components/theme-provider"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Moon, Sun } from "lucide-react"

export function ThemeToggle() {
  const { theme, setTheme } = useTheme()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon">
          <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">切换主题</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => setTheme("light")}>
          亮色
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("dark")}>
          暗色
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("system")}>
          跟随系统
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
```

## 应用主题提供者

在应用根布局中应用主题提供者：

```tsx
// app/layout.tsx
import { ThemeProvider } from "@/components/theme-provider"
import "./globals.css"

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <head />
      <body>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  )
}
```

## 暗色模式设计原则

为了确保明暗两种模式下的良好用户体验，我们遵循以下设计原则：

### 1. 降低对比度，避免眩光

- 暗色模式下不使用纯黑色背景，而是使用深灰色（#121212、#1F1F1F等）
- 避免在暗色模式下使用大面积的纯白色，改用浅灰色（#E0E0E0、#F5F5F5等）
- 减少文本与背景的对比度，但仍需符合无障碍标准（至少4.5:1）

### 2. 降低饱和度

- 暗色模式下降低颜色的饱和度，防止颜色过于鲜艳导致视觉疲劳
- 主色调保持相似色相，但调整亮度和饱和度适应暗色背景

### 3. 增加元素间距和边框

- 暗色模式下适当增加元素间距，提高可读性
- 使用微妙的边框或阴影区分界面元素，替代亮色模式下的背景色差异

### 4. 状态颜色调整

- 错误、警告、成功等状态颜色在暗色模式下需调整为更柔和的版本
- 保持颜色语义一致性，同时调整亮度和饱和度

## 组件在明暗模式下的表现

### 按钮组件

在明暗模式下的按钮样式对比：

```jsx
<Button variant="default">默认按钮</Button>
```

| 模式 | 背景色 | 文本色 | 悬停状态 |
|-----|-------|-------|---------|
| 亮色 | var(--primary) | var(--primary-foreground) | 背景色略深 |
| 暗色 | var(--primary) | var(--primary-foreground) | 背景色略浅 |

### 卡片组件

卡片在明暗模式下的样式调整：

```jsx
<Card>
  <CardHeader>
    <CardTitle>卡片标题</CardTitle>
    <CardDescription>卡片描述</CardDescription>
  </CardHeader>
  <CardContent>
    <p>卡片内容</p>
  </CardContent>
</Card>
```

| 模式 | 背景色 | 文本色 | 边框 |
|-----|-------|-------|------|
| 亮色 | var(--card) (白色) | var(--card-foreground) (深色) | 浅灰色边框 |
| 暗色 | var(--card) (深灰色) | var(--card-foreground) (浅色) | 深色半透明边框 |

### 表单元素

表单元素在明暗模式下的样式：

```jsx
<div className="space-y-4">
  <Input placeholder="输入框" />
  <Textarea placeholder="文本区域" />
  <Select>
    <SelectTrigger>
      <SelectValue placeholder="选择选项" />
    </SelectTrigger>
    <SelectContent>
      <SelectItem value="option1">选项1</SelectItem>
      <SelectItem value="option2">选项2</SelectItem>
    </SelectContent>
  </Select>
</div>
```

| 模式 | 背景色 | 文本色 | 边框 | 焦点状态 |
|-----|-------|-------|------|---------|
| 亮色 | 白色/透明 | 深色文本 | 浅灰色边框 | 主色调环 |
| 暗色 | 深色/半透明 | 浅色文本 | 深色半透明边框 | 主色调环 |

## 图像和媒体在明暗模式下的处理

### 图像处理

为确保图像在明暗模式下都清晰可见：

1. **应用图像反转**：对界面图标和简单图像可使用CSS滤镜反转：

```css
.dark .invertible-image {
  filter: invert(1);
}
```

2. **提供备选图像**：为复杂图像提供明暗两个版本：

```jsx
<img 
  src={theme === "dark" ? "/images/logo-dark.svg" : "/images/logo-light.svg"} 
  alt="Logo" 
/>
```

3. **调整图像明暗度**：使用CSS滤镜微调图像明暗度：

```css
.dark .adjust-image {
  filter: brightness(0.8);
}
```

### 视频处理

视频在暗色模式下通常需要降低亮度和对比度：

```css
.dark video {
  filter: brightness(0.8) contrast(0.95);
}
```

## 系统集成

### 系统主题检测

使用媒体查询检测系统主题偏好：

```js
const systemPrefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
```

### 主题持久化

将用户主题偏好保存在localStorage中：

```js
// 保存主题偏好
localStorage.setItem("theme", "dark")

// 获取主题偏好
const savedTheme = localStorage.getItem("theme") || "system"
```

### 手动切换与系统同步

实现手动切换和系统同步的策略：

1. 用户手动设置的主题优先级高于系统主题
2. 系统主题变化时，若用户设置为"跟随系统"，则应用跟随变化
3. 首次访问时默认使用系统主题

## 最佳实践

### 主题切换应避免闪烁

防止主题切换时的闪烁问题：

```html
<!-- 在HTML头部添加脚本，尽早应用主题 -->
<script>
  function setTheme() {
    const theme = localStorage.getItem("theme") || "system"
    const root = document.documentElement
    
    if (theme === "system") {
      const systemTheme = window.matchMedia("(prefers-color-scheme: dark)").matches
        ? "dark" : "light"
      root.classList.add(systemTheme)
    } else {
      root.classList.add(theme)
    }
  }
  setTheme()
</script>
```

### 使用语义化颜色变量

避免直接使用颜色代码，而是使用语义化变量：

```css
/* 不推荐 */
.element {
  color: #000000;
  background-color: #ffffff;
}

/* 推荐 */
.element {
  color: var(--foreground);
  background-color: var(--background);
}
```

### 确保足够的对比度

在两种模式下都确保文本与背景有足够对比度：

```jsx
// 使用适当的对比度类
<p className="text-foreground">主要文本内容</p>
<p className="text-muted-foreground">次要文本内容</p>
```

## 主题扩展和自定义

除了基本的明暗主题外，系统还支持创建额外的自定义主题：

### 自定义主题定义

```css
/* 自定义蓝色主题 */
[data-theme="blue"] {
  --primary: oklch(0.650 0.280 250.42);
  --primary-foreground: oklch(0.985 0 0);
  
  /* 其他颜色变量... */
}

/* 自定义绿色主题 */
[data-theme="green"] {
  --primary: oklch(0.568 0.191 159.76);
  --primary-foreground: oklch(0.985 0 0);
  
  /* 其他颜色变量... */
}
```

### 主题切换实现

扩展主题切换以支持自定义主题：

```tsx
type Theme = "light" | "dark" | "system" | "blue" | "green"

// 在ThemeToggle组件中添加自定义主题选项
<DropdownMenuItem onClick={() => setTheme("blue")}>
  蓝色主题
</DropdownMenuItem>
<DropdownMenuItem onClick={() => setTheme("green")}>
  绿色主题
</DropdownMenuItem>
```

### 品牌主题创建指南

创建品牌主题时应遵循以下步骤：

1. 从品牌主色调开始，定义主色调的10个亮度级别
2. 创建语义化颜色映射，将品牌色映射到界面元素
3. 确保明暗两种模式下都有良好的对比度
4. 测试各种组件在新主题下的表现
5. 提供简单的方式让用户切换到品牌主题 