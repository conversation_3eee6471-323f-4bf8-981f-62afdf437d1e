"use client"

import React from "react"
import { 
  TruncateText as CommonTruncateText, 
  TruncateTextProps as CommonTruncateTextProps 
} from "@/components/common-custom/truncate-text"

// ============================================================================
// 项目特定文本截断组件 - 保持与现有接口的兼容性
// ============================================================================

/**
 * 项目特定的文本截断组件属性（兼容现有接口）
 */
interface TruncateTextProps {
  /**
   * 要显示的文本内容
   */
  text: string
  
  /**
   * 自定义样式类
   */
  className?: string
  
  /**
   * 要渲染的HTML元素，默认为span
   */
  as?: React.ElementType
  
  /**
   * 最大显示行数，默认为1
   */
  lines?: number
  
  /**
   * 是否显示提示，默认为true
   */
  showTooltip?: boolean
  
  /**
   * 最大宽度，如"200px"、"10rem"等
   */
  maxWidth?: string

  /**
   * 通用组件的额外配置
   */
  truncateProps?: Omit<CommonTruncateTextProps, keyof TruncateTextProps>
}

/**
 * 项目特定的文本截断组件
 * 
 * 基于通用文本截断组件，保持与现有项目接口的完全兼容性
 * 同时提供通用组件的所有高级功能
 * 
 * @param text 要显示的文本内容
 * @param className 自定义样式类
 * @param as 要渲染的HTML元素，默认为span
 * @param lines 最大显示行数，默认为1
 * @param showTooltip 是否显示提示，默认为true
 * @param maxWidth 最大宽度，如"200px"、"10rem"等
 * 
 * @example
 * ```tsx
 * // 基础用法（与现有代码兼容）
 * <TruncateText text="长文本内容" maxWidth="200px" />
 * 
 * // 多行截断
 * <TruncateText text="长文本内容" lines={3} maxWidth="300px" />
 * 
 * // 使用高级功能
 * <TruncateText
 *   text="长文本内容"
 *   maxWidth="200px"
 *   truncateProps={{
 *     expandable: true,
 *     position: "middle",
 *     onExpandChange: (expanded) => console.log(expanded)
 *   }}
 * />
 * ```
 */
export function TruncateText({
  text,
  className,
  as: Component = "span",
  lines = 1,
  showTooltip = true,
  maxWidth,
  truncateProps = {}
}: TruncateTextProps) {
  // 如果指定了自定义元素类型，需要包装一下
  if (Component !== "span" && Component !== "div") {
    return (
      <Component className={className}>
        <CommonTruncateText
          text={text}
          lines={lines}
          showTooltip={showTooltip}
          maxWidth={maxWidth}
          {...truncateProps}
        />
      </Component>
    )
  }

  return (
    <CommonTruncateText
      text={text}
      className={className}
      lines={lines}
      showTooltip={showTooltip}
      maxWidth={maxWidth}
      {...truncateProps}
    />
  )
}

// 导出类型
export type { CommonTruncateTextProps as ExtendedTruncateTextProps }
