# 项目文档导航

欢迎来到 web-template-demo 项目文档中心。本文档集合为项目开发提供全面的规范和指导。

## 📚 文档结构

### 🎨 设计规范 (`design/`)
- [设计哲学](./design/design-philosophy.md) - 核心设计理念与原则
- [色彩系统](./design/color-system.md) - 色彩规范与使用指南
- [排版系统](./design/typography.md) - 字体与排版规范
- [布局网格](./design/layout-grid.md) - 网格系统与布局规范
- [组件系统](./design/component-system.md) - 组件设计规范
- [交互设计](./design/interaction-design.md) - 交互模式与规范
- [主题与暗色模式](./design/theme-dark-mode.md) - 主题系统规范
- [表单设计](./design/form-design.md) - 表单组件设计规范
- [图标系统](./design/icon-system.md) - 图标使用规范
- [动效系统](./design/animation-system.md) - 动画与过渡规范
- [媒体规范](./design/media-guidelines.md) - 图片与媒体使用规范

### 💻 开发规范 (`development/`)
- [组件开发规范](./development/component-standards.md) - 通用组件开发标准
- [API接口规范](./development/api-standards.md) - 请求与接口规范
- [数据可视化规范](./development/data-visualization.md) - 图表与可视化规范
- [MCP工具使用指南](./development/mcp-tools-guide.md) - MCP工具集使用规范
- [实施指南](./development/implementation-guide.md) - 项目实施与开发指南

### 🤝 协作规范 (`collaboration/`)
- [AI协作规范](./collaboration/ai-collaboration.md) - 多维思考与AI协作规则
- [请求响应规范](./collaboration/request-response.md) - 请求处理与响应规范

### 📋 快速参考
- [开发规则速查](./rules.md) - 核心开发规则与检查清单

## 🚀 快速开始

1. **新手开发者**：建议从 [设计哲学](./design/design-philosophy.md) 和 [实施指南](./development/implementation-guide.md) 开始
2. **组件开发**：重点阅读 [组件开发规范](./development/component-standards.md) 和 [MCP工具使用指南](./development/mcp-tools-guide.md)
3. **AI协作**：必读 [AI协作规范](./collaboration/ai-collaboration.md)

## 📝 文档维护

本文档集基于项目实际需求整合而成，定期更新以保持与项目发展同步。

如有疑问或建议，请通过项目协作流程提出。
