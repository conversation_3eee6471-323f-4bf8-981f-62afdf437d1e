# 项目文档导航

欢迎来到 web-template-demo 项目文档中心。本文档集合为项目开发提供全面的规范和指导。

## 📚 文档分类结构

### 🌐 通用规范 (`universal-*`)
适用于前后端开发的所有场景：
- [AI协作规范](./universal-ai-collaboration.md) - 多维思考与AI协作规则
- [MCP工具使用指南](./universal-mcp-tools.md) - MCP工具集使用规范
- [API接口规范](./universal-api-standards.md) - 通用API设计与实现规范

### 🎨 前端规范 (`frontend-*`)
专门针对前端开发的规范：
- [设计哲学](./frontend-design-philosophy.md) - 前端设计理念与原则
- [组件开发规范](./frontend-component-standards.md) - React组件开发标准
- [色彩系统](./design/color-system.md) - 色彩规范与使用指南
- [排版系统](./design/typography.md) - 字体与排版规范
- [布局网格](./design/layout-grid.md) - 网格系统与布局规范

### 🚀 项目特定 (`project-*`)
针对当前web-template-demo项目的规范：
- [实施指南](./project-implementation-guide.md) - 项目实施与开发指南
- [请求响应规范](./collaboration/request-response.md) - 项目特定的请求处理规范

### 📋 快速参考
- [核心指导规范](./GUIDE.md) - 50字总结性指导规范
- [开发规则速查](./rules.md) - 核心开发规则与检查清单
- [文档整合总结](./SUMMARY.md) - 文档整合工作总结

### 🛠️ 工作流程指南
- [任务拆分记录模板](./task-breakdown-template.md) - 任务拆分和记录的标准模板
- [规范更新指南](./standards-update-guide.md) - 优秀规范识别和文档更新流程

## 🚀 快速开始

### 根据角色选择文档
1. **新手开发者**：
   - 从 [核心指导规范](./GUIDE.md) 开始
   - 阅读 [前端设计哲学](./frontend-design-philosophy.md)
   - 学习 [项目实施指南](./project-implementation-guide.md)

2. **前端开发者**：
   - 重点阅读 [前端组件开发规范](./frontend-component-standards.md)
   - 掌握 [MCP工具使用指南](./universal-mcp-tools.md)
   - 参考设计规范文档

3. **后端开发者**：
   - 重点阅读 [通用API接口规范](./universal-api-standards.md)
   - 掌握 [MCP工具使用指南](./universal-mcp-tools.md)

4. **AI协作者**：
   - 必读 [通用AI协作规范](./universal-ai-collaboration.md)
   - 掌握 [MCP工具使用指南](./universal-mcp-tools.md)

### 日常开发参考
- **快速检查**：使用 [开发规则速查](./rules.md)
- **核心原则**：参考 [核心指导规范](./GUIDE.md)
- **问题解决**：遵循MCP工具使用规范

## 📝 文档维护

本文档集基于项目实际需求整合而成，采用分类命名规范：
- `universal-*`：前后端通用规范
- `frontend-*`：前端专用规范
- `project-*`：项目特定规范

定期更新以保持与项目发展同步。如有疑问或建议，请通过项目协作流程提出。
