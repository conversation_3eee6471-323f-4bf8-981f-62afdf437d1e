# 通用组件规范

本文档规范了项目中常用的自定义组件，包括导航栏、面包屑等，确保在整个应用中保持一致的用户体验和开发规范。

## 导航组件

### 全局导航栏 (GlobalNavigation)

全局导航栏是应用的主要导航结构，提供应用的主要功能区域入口。

#### 组件结构

```jsx
<GlobalNavigation />
```

#### 实现细节

- 使用`useSidebar` hook管理侧边栏状态
- 根据当前路径自动高亮对应导航项
- 响应式设计：在移动设备上自动收起
- 支持团队切换、项目导航和用户菜单

#### 用法示例

```jsx
import { GlobalNavigation } from "@/components/navigation/global-navigation"

export default function Layout({ children }) {
  return (
    <div className="flex min-h-screen">
      <GlobalNavigation />
      <main className="flex-1">{children}</main>
    </div>
  )
}
```

### 导航配置

导航使用配置文件驱动，便于维护和扩展：

```js
// lib/navigation-config.js
export function getNavigationConfig() {
  return {
    // 主导航项
    navMain: [
      {
        title: "仪表盘",
        icon: "dashboard",
        url: "/dashboard",
      },
      {
        title: "项目",
        icon: "projects",
        url: "/projects",
        items: [
          {
            title: "所有项目",
            url: "/projects",
          },
          {
            title: "新建项目",
            url: "/projects/new",
          }
        ]
      },
      // 更多导航项...
    ],
    
    // 团队配置
    teams: [
      {
        id: "team-1",
        name: "团队一",
        logo: "team",
      },
      // 更多团队...
    ],
    
    // 项目列表
    projects: [
      {
        name: "项目一",
        url: "/projects/1",
        icon: "project",
      },
      // 更多项目...
    ],
    
    // 当前用户信息
    user: {
      name: "张三",
      email: "<EMAIL>",
      avatar: "/avatars/user.png",
    },
  }
}

// 检查当前路径是否应显示导航
export function useShowNavigation() {
  const pathname = usePathname()
  return !pathname.startsWith("/auth/") && pathname !== "/"
}
```

## 页面头部组件

### 带面包屑的页面头部 (HeaderWithBreadcrumb)

为页面提供一致的头部区域，包含面包屑导航和操作按钮。

#### 组件属性

| 属性名 | 类型 | 说明 |
|-------|------|-----|
| items | BreadcrumbItem[] | 面包屑项数组 |
| className | string | 自定义类名 |
| actions | ReactNode | 右侧操作区域内容 |

```typescript
// 面包屑项类型定义
type BreadcrumbItem = {
  href?: string      // 链接地址
  label: string      // 显示文本
  isCurrent?: boolean  // 是否为当前页
}
```

#### 实现细节

- 自动处理面包屑显示逻辑：超过3项时显示第一项、省略号和最后一项
- 使用客户端路由导航
- 提供右侧操作区域插槽
- 自动集成侧边栏触发器

#### 用法示例

```jsx
import { HeaderWithBreadcrumb } from "@/components/custom/breadcrumb"
import { Button } from "@/components/ui/button"

export default function ProjectPage() {
  const breadcrumbItems = [
    { href: "/dashboard", label: "仪表盘" },
    { href: "/projects", label: "项目" },
    { href: "/projects/1", label: "项目一", isCurrent: true }
  ]
  
  return (
    <div>
      <HeaderWithBreadcrumb 
        items={breadcrumbItems}
        actions={
          <>
            <Button variant="outline">编辑</Button>
            <Button>新建</Button>
          </>
        }
      />
      <div className="p-6">
        {/* 页面内容 */}
      </div>
    </div>
  )
}
```

## 侧边栏组件 (Sidebar)

提供可折叠的应用侧边栏，支持多种布局模式。

### 组件结构

```jsx
<Sidebar>
  <SidebarHeader>
    {/* 侧边栏头部内容 */}
  </SidebarHeader>
  <SidebarContent>
    {/* 侧边栏主要内容 */}
  </SidebarContent>
  <SidebarFooter>
    {/* 侧边栏底部内容 */}
  </SidebarFooter>
  <SidebarRail />
</Sidebar>
```

### 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|-----|
| collapsible | "none" \| "icon" \| "full" | "none" | 折叠模式 |
| defaultCollapsed | boolean | false | 默认是否折叠 |
| className | string | - | 自定义类名 |

### 侧边栏状态管理

使用`useSidebar` hook管理侧边栏状态：

```jsx
const { isOpen, isCollapsed, setOpen, setCollapsed, toggle, toggleCollapsed } = useSidebar()
```

### 侧边栏触发器

用于控制侧边栏打开/关闭的按钮组件：

```jsx
<SidebarTrigger />
```

### 用法示例

```jsx
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarRail, useSidebar } from "@/components/custom/sidebar"

export function AppLayout({ children }) {
  return (
    <div className="flex min-h-screen">
      <Sidebar collapsible="icon">
        <SidebarHeader>
          {/* 应用 logo */}
        </SidebarHeader>
        <SidebarContent>
          {/* 导航菜单 */}
        </SidebarContent>
        <SidebarFooter>
          {/* 用户信息 */}
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
      <main className="flex-1">
        {children}
      </main>
    </div>
  )
}
```

## 页面布局组件

### 页面容器 (PageContainer)

为页面内容提供一致的容器样式和间距。

```jsx
<PageContainer>
  <PageHeader title="页面标题" description="页面描述" />
  <PageContent>
    {/* 页面主要内容 */}
  </PageContent>
</PageContainer>
```

### 分栏布局 (SplitLayout)

提供左右分栏或上下分栏的布局结构。

```jsx
<SplitLayout>
  <SplitLayoutLeft>
    {/* 左侧内容 */}
  </SplitLayoutLeft>
  <SplitLayoutRight>
    {/* 右侧内容 */}
  </SplitLayoutRight>
</SplitLayout>
```

支持不同分栏比例：

```jsx
<SplitLayout ratio="1:2"> {/* 左侧1份，右侧2份宽度 */}
  {/* 分栏内容 */}
</SplitLayout>
```

## 最佳实践

1. **组件组合**：优先使用现有组件组合，而非创建新组件
2. **响应式设计**：所有自定义组件应支持各种屏幕尺寸
3. **保持一致性**：遵循已有的设计语言和交互模式
4. **无障碍支持**：确保组件支持键盘导航和屏幕阅读器
5. **性能优化**：大型组件使用React.memo、动态导入等优化手段

## 扩展与定制

当需要创建新的通用组件时，应遵循以下原则：

1. 组件放置在合适的目录（custom、layout、navigation等）
2. 提供完整的TypeScript类型定义
3. 支持基本的自定义选项（className、style等）
4. 实现必要的响应式行为
5. 编写详细的组件文档和使用示例 