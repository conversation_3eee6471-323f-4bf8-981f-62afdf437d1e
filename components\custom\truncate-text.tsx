"use client"

import React from "react"
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"

interface TruncateTextProps {
  text: string
  className?: string
  as?: React.ElementType
  lines?: number
  showTooltip?: boolean
  maxWidth?: string
}

/**
 * 文本截断组件，超出显示省略号并在悬浮时显示完整内容
 * @param text 要显示的文本内容
 * @param className 自定义样式类
 * @param as 要渲染的HTML元素，默认为span
 * @param lines 最大显示行数，默认为1
 * @param showTooltip 是否显示提示，默认为true
 * @param maxWidth 最大宽度，如"200px"、"10rem"等
 */
export function TruncateText({
  text,
  className,
  as: Component = "span",
  lines = 1,
  showTooltip = true,
  maxWidth,
}: TruncateTextProps) {
  if (!text) return null
  
  // 设置行截断样式
  const truncateStyle = lines === 1
    ? "truncate"
    : `line-clamp-${lines}`
  
  // 自定义样式，包括最大宽度
  const style = maxWidth ? { maxWidth } : undefined;
  
  const content = (
    <Component
      className={cn(
        truncateStyle,
        "inline-block w-full",
        className
      )}
      title={!showTooltip ? text : undefined}
      style={style}
    >
      {text}
    </Component>
  )
  
  // 如果不显示tooltip或文本为空，直接返回文本元素
  if (!showTooltip || !text.trim()) {
    return content
  }
  
  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          {content}
        </TooltipTrigger>
        <TooltipContent className="max-w-[280px] break-words text-xs">
          <p>{text}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
} 