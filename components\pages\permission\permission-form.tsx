"use client"

import React from "react"
import { useState, useEffect, useRef } from "react"
import { type PermissionNode, PermissionType } from "@/types/permission"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { Folder, FileText, Settings, LayoutGrid, CheckCircle2 } from "lucide-react"
import { IconSelector } from "@/components/custom/icon-selector"
import { cn } from "@/lib/utils"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Checkbox } from "@/components/ui/checkbox"

interface PermissionFormProps {
  selectedNode: PermissionNode | null
  permissions: PermissionNode[]
  onSave: (node: PermissionNode) => void
  onCancel: () => void
  getNodePath: (nodeId: string) => string
}

export default function PermissionForm({
  selectedNode,
  permissions,
  onSave,
  onCancel,
  getNodePath,
}: PermissionFormProps) {
  const [formData, setFormData] = useState<Partial<PermissionNode> | null>(null)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [currentPath, setCurrentPath] = useState<string>("")
  const [isExternalLink, setIsExternalLink] = useState<boolean>(false)
  const { toast } = useToast()
  const pendingChangesRef = useRef<{ field: string; value: any } | null>(null)

  useEffect(() => {
    if (selectedNode) {
      setFormData({ ...selectedNode })
      // 检查是否为外部链接
      setIsExternalLink(selectedNode.isExternalLink || false)
      // 更新路径
      setCurrentPath(getNodePath(selectedNode.id))
    } else {
      setFormData(null)
      setCurrentPath("")
      setIsExternalLink(false)
    }
    setErrors({})
  }, [selectedNode, getNodePath])

  // 使用useEffect处理状态更新
  useEffect(() => {
    const processPendingChanges = () => {
      if (pendingChangesRef.current && formData && formData.id) {
        const { field } = pendingChangesRef.current;
        
        // 更新当前路径
        if (field === 'parentId' || field === 'name') {
          onSave(formData as PermissionNode);
          setCurrentPath(getNodePath(formData.id as string));
        } else {
          onSave(formData as PermissionNode);
        }
        
        // 清除待处理的变更
        pendingChangesRef.current = null;
      }
    };
    
    processPendingChanges();
  }, [formData, getNodePath, onSave]);

  // 修改 handleChange 函数，处理父级权限和顺序变更
  const handleChange = (field: string, value: any) => {
    setFormData((prev) => {
      if (!prev) return null

      // 创建更新后的节点
      const updatedNode = { ...prev, [field]: value }

      // 如果是类型变更，需要处理特殊逻辑
      if (field === "type") {
        // 根据类型设置默认值
        if (value === PermissionType.Directory) {
          updatedNode.parentId = undefined // 目录没有父级
        }
      }

      return updatedNode
    })

    // 记录待处理的变更，让useEffect处理
    pendingChangesRef.current = { field, value };

    // 清除该字段的错误（如果存在）
    if (errors[field]) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  // 检查是否可以添加特定类型的节点到父节点
  const canAddToParent = (parentType: PermissionType | null, childType: PermissionType): boolean => {
    if (!formData) return false;
    
    // 目录可以作为顶级或者放在目录下
    if (childType === PermissionType.Directory) {
      return parentType === null || parentType === PermissionType.Directory
    }

    // 菜单可以作为顶级，或者目录/菜单的子级
    if (childType === PermissionType.Menu) {
      return parentType === null || parentType === PermissionType.Directory || parentType === PermissionType.Menu
    }

    // 按钮只能添加到菜单下
    if (childType === PermissionType.Button) {
      return parentType === PermissionType.Menu
    }

    return false
  }

  const getParentOptions = () => {
    if (!formData) return [];
    
    const options: { id: string; name: string; type: PermissionType }[] = []

    const traverse = (nodes: PermissionNode[], path = "") => {
      nodes.forEach((node) => {
        // 跳过当前节点及其子节点，防止循环引用
        if (formData.id && node.id === formData.id) return

        const newPath = path ? `${path} > ${node.name}` : node.name

        // 根据规则过滤可选的父节点
        if (canAddToParent(node.type, formData.type as PermissionType)) {
          options.push({ id: node.id, name: newPath, type: node.type })
        }

        if (node.children) {
          traverse(node.children, newPath)
        }
      })
    }

    traverse(permissions)
    return options
  }

  const parentOptions = getParentOptions()

  // 渲染权限类型卡片
  const renderTypeCard = (type: PermissionType, icon: React.ReactNode, title: string, description: string) => {
    // 如果formData不存在，直接返回不可点击状态
    if (!formData) {
      return (
        <div className="flex-1 py-1 opacity-60 cursor-not-allowed">
          <div className="flex items-center gap-3 p-3 border rounded-md bg-background">
            <div className="flex items-center justify-center w-8 h-8 bg-muted/50 rounded-md">
              <div className="h-5 w-5 text-muted-foreground">{icon}</div>
            </div>
            <div className="flex-1 flex flex-col">
              <span className="text-sm font-medium">{title}</span>
              <span className="text-xs text-muted-foreground">{description}</span>
            </div>
          </div>
        </div>
      )
    }
    
    const isSelected = formData.type === type;
    
    // 检查是否可以切换到此类型
    let canSwitch = true;
    let errorMessage = "";
    
    // 非新建状态且非当前类型时，检查是否可以切换
    if (formData.id && !isSelected) {
      // 检查是否有子节点
      const hasChildren = !!(formData.children && formData.children.length > 0);
      
      // 获取父节点类型
      let parentType: PermissionType | null = null;
      if (formData.parentId) {
        const findParentType = (nodes: PermissionNode[], id: string): PermissionType | null => {
          for (const node of nodes) {
            if (node.id === id) {
              return node.type;
            }
            if (node.children && node.children.length > 0) {
              const found = findParentType(node.children, id);
              if (found) return found;
            }
          }
          return null;
        };
        
        parentType = findParentType(permissions, formData.parentId);
      }
      
      // 按钮类型的限制
      if (type === PermissionType.Button) {
        if (hasChildren) {
          canSwitch = false;
          errorMessage = "按钮不能有子节点，请先删除所有子节点";
        } else if (!formData.parentId || parentType !== PermissionType.Menu) {
          canSwitch = false;
          errorMessage = "按钮只能放在菜单下，请先调整节点位置";
        }
      }
      
      // 目录类型的限制 - 从菜单切换到目录时，允许保持原位置，只要父级是目录
      if (type === PermissionType.Directory && formData.parentId) {
        if (parentType !== PermissionType.Directory) {
          canSwitch = false;
          errorMessage = "目录只能放在目录下或作为顶级节点，请先调整节点位置";
        }
      }
      
      // 菜单类型的限制
      if (type === PermissionType.Menu) {
        if (formData.parentId && parentType !== PermissionType.Directory && parentType !== PermissionType.Menu) {
          canSwitch = false;
          errorMessage = "菜单只能放在目录或菜单下，请先调整节点位置";
        }
      }
    }
    
    const isDisabled = !isSelected && !canSwitch;
    
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div
              className={cn(
                "flex-1 cursor-pointer",
                isDisabled ? "opacity-60 cursor-not-allowed" : ""
              )}
              onClick={() => {
                if (isSelected || canSwitch) {
                  handleChange("type", type);
                } else if (errorMessage) {
                  toast({
                    title: "无法切换类型",
                    description: errorMessage,
                    variant: "destructive",
                  });
                }
              }}
            >
              <div className={cn(
                "flex items-center gap-3 p-3 border rounded-md transition-colors bg-background",
                isSelected 
                  ? "border-primary" 
                  : "hover:border-muted-foreground"
              )}>
                <div className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-md",
                  isSelected 
                    ? "bg-primary/10" 
                    : "bg-muted/50"
                )}>
                  <div className={cn(
                    "h-5 w-5", 
                    isSelected ? "text-primary" : "text-muted-foreground"
                  )}>
                    {icon}
                  </div>
                </div>
                <div className="flex-1 flex flex-col">
                  <span className="text-sm font-medium">{title}</span>
                  <span className="text-xs text-muted-foreground">{description}</span>
                </div>
                {isSelected && (
                  <div className="ml-auto h-5 w-5 flex items-center justify-center">
                    <CheckCircle2 className="h-5 w-5 text-primary" />
                  </div>
                )}
              </div>
            </div>
          </TooltipTrigger>
          {isDisabled && errorMessage && (
            <TooltipContent side="bottom">
              <p className="text-xs">{errorMessage}</p>
            </TooltipContent>
          )}
        </Tooltip>
      </TooltipProvider>
    );
  };

  if (!formData) {
    return (
      <div className="h-full border rounded-md flex items-center justify-center">
        <div className="flex flex-col items-center">
          <FileText className="h-12 w-12 text-muted-foreground/30 mb-3" />
          <p className="text-sm">从左侧树中选择一个权限进行配置</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full overflow-auto border rounded-md">
      <div className="p-4 border-b">
        <div className="flex justify-between items-center">
          <h3 className="text-sm font-medium flex items-center gap-2">
            <FileText className="h-4 w-4 text-primary" />
            权限信息
          </h3>
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-muted-foreground">启用</span>
            <Switch
              checked={formData.enabled !== false}
              onCheckedChange={(checked) => handleChange("enabled", checked)}
              className="scale-110"
            />
          </div>
        </div>
      </div>

      <div className="px-4 py-4">
        {/* 当前路径显示 */}
        {currentPath && (
          <div className="bg-muted/30 px-3 py-2 rounded-md mb-5">
            <div className="flex items-center">
              <span className="font-medium text-sm">当前路径:</span>
              <span className="ml-2 text-sm text-muted-foreground">{currentPath}</span>
            </div>
          </div>
        )}

        {/* 基本信息部分 */}
        <div className="space-y-6">
          <div className="pb-2">
            <div className="flex justify-between items-center mb-3">
              <Label className="text-sm font-medium">权限类型</Label>
            </div>
            <div className="flex gap-3">
              {renderTypeCard(
                PermissionType.Directory,
                <Folder className="h-5 w-5" />,
                "目录",
                "(无路由)"
              )}
              
              {renderTypeCard(
                PermissionType.Menu,
                <FileText className="h-5 w-5" />,
                "菜单",
                "(需要路由)"
              )}
              
              {renderTypeCard(
                PermissionType.Button,
                <Settings className="h-5 w-5" />,
                "按钮",
                "(需要标识)"
              )}
            </div>
          </div>

          <div className="pt-4">
            <h4 className="text-sm font-medium mb-4 flex items-center">
              <FileText className="h-4 w-4 text-primary mr-1.5" />
              基本信息
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium text-muted-foreground">
                  权限名称 <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="name"
                  value={formData.name || ""}
                  onChange={(e) => handleChange("name", e.target.value)}
                  maxLength={20}
                  className={cn(errors.name ? "border-destructive" : "", "font-medium")}
                  placeholder="请输入权限名称"
                />
                {errors.name && <p className="text-xs text-destructive">{errors.name}</p>}
                <p className="text-xs text-muted-foreground">最多20个字符</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="permissionId" className="text-sm font-medium text-muted-foreground">
                  权限标识 <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="permissionId"
                  value={formData.permissionId || ""}
                  onChange={(e) => handleChange("permissionId", e.target.value)}
                  className={cn(errors.permissionId ? "border-destructive" : "", "font-medium")}
                  placeholder="请输入权限标识"
                />
                {errors.permissionId && <p className="text-xs text-destructive">{errors.permissionId}</p>}
                <p className="text-xs text-muted-foreground">权限标识用于前端鉴权，建议保持系统唯一性</p>
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="icon" className="text-sm font-medium text-muted-foreground">图标</Label>
                <IconSelector value={formData.icon || ""} onChange={(value) => handleChange("icon", value)} />
              </div>
            </div>
          </div>

          {/* 菜单特有配置 */}
          {formData.type === PermissionType.Menu && (
            <div className="pt-4">
              <h4 className="text-sm font-medium mb-4 flex items-center">
                <FileText className="h-4 w-4 text-primary mr-1.5" />
                路由配置
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                <div className="space-y-2">
                  <Label htmlFor="path" className="text-sm font-medium text-muted-foreground">
                    路由路径 <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id="path"
                    name="path"
                    value={formData.path || ""}
                    onChange={(e) => handleChange("path", e.target.value)}
                    className={cn(errors.path ? "border-destructive" : "", "font-medium")}
                    placeholder="/system/role"
                  />
                  {errors.path && <p className="text-xs text-destructive">{errors.path}</p>}
                  <p className="text-xs text-muted-foreground">内部路由以/开头，例如：/system/role</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="componentPath" className="text-sm font-medium text-muted-foreground">
                    组件路径
                  </Label>
                  <Input
                    id="componentPath"
                    name="componentPath"
                    value={formData.componentPath || ""}
                    onChange={(e) => handleChange("componentPath", e.target.value)}
                    className="font-medium"
                    placeholder="如: /pages/system/user"
                  />
                  <p className="text-xs text-muted-foreground">前端组件的路径，用于动态加载</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="isExternalLink" className="text-sm font-medium text-muted-foreground">
                    外部链接
                  </Label>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="isExternalLink"
                      checked={formData.isExternalLink || false}
                      onCheckedChange={(checked) => handleChange("isExternalLink", !!checked)}
                    />
                    <label
                      htmlFor="isExternalLink"
                      className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      是否为外部链接
                    </label>
                  </div>
                  <p className="text-xs text-muted-foreground">外部链接将在新窗口中打开</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="openInNewTab" className="text-sm font-medium text-muted-foreground">
                    新页面打开
                  </Label>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="openInNewTab"
                      checked={formData.openInNewTab || false}
                      onCheckedChange={(checked) => handleChange("openInNewTab", !!checked)}
                    />
                    <label
                      htmlFor="openInNewTab"
                      className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      在新窗口中打开链接
                    </label>
                  </div>
                  <p className="text-xs text-muted-foreground">勾选后将在新窗口中打开链接</p>
                </div>
              </div>
            </div>
          )}

          {/* 位置与顺序配置 */}
          <div className="pt-4">
            <h4 className="text-sm font-medium mb-4 flex items-center">
              <LayoutGrid className="h-4 w-4 text-primary mr-1.5" />
              位置与顺序
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
              {formData.type !== PermissionType.Directory && parentOptions.length > 0 && (
                <div className="space-y-2">
                  <Label htmlFor="parentId" className="text-sm font-medium text-muted-foreground">父级权限</Label>
                  <Select value={formData.parentId || ""} onValueChange={(value) => handleChange("parentId", value)}>
                    <SelectTrigger className={cn(errors.parentId ? "border-destructive" : "", "font-medium")}>
                      <SelectValue placeholder="选择父级权限" />
                    </SelectTrigger>
                    <SelectContent>
                      {parentOptions.map((option) => (
                        <SelectItem key={option.id} value={option.id}>
                          {option.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.parentId && <p className="text-xs text-destructive">{errors.parentId}</p>}
                  <p className="text-xs text-muted-foreground">
                    {formData.type === PermissionType.Menu ? "菜单可以放在目录或其他菜单下" : "按钮只能放在菜单下"}
                  </p>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="order" className="text-sm font-medium text-muted-foreground">显示顺序</Label>
                <Input
                  id="order"
                  type="number"
                  value={formData.order || "0"}
                  onChange={(e) => handleChange("order", Number.parseInt(e.target.value))}
                  min="0"
                  className="font-medium"
                />
                <p className="text-xs text-muted-foreground">决定在菜单中的显示顺序</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
