# 开发规则速查

本文档提供项目开发的核心规则和检查清单，供快速参考使用。

## 🎯 核心开发原则

### 1. MCP工具优先
- ✅ **优先使用MCP工具**：充分利用已配置的工具链
- ✅ **遵循工具规范**：按照MCP工具使用规范合理选择工具
- ✅ **批量操作优先**：使用批量操作提高效率
- ✅ **重要操作确认**：使用feedback-enhanced工具确认重要操作

### 2. 设计一致性
- ✅ **遵循设计哲学**：数字空间疗愈，可呼吸的界面
- ✅ **使用语义化色彩**：优先使用CSS变量而非硬编码颜色
- ✅ **保持圆润边角**：使用16px/24px圆角体系
- ✅ **充足留白**：采用8px基准单位的间距系统

### 3. 组件开发
- ✅ **明确组件分类**：ui/common-custom/project-custom
- ✅ **完整类型定义**：所有Props都要有TypeScript类型和JSDoc
- ✅ **支持className**：所有组件都要支持className属性
- ✅ **使用cn函数**：合并类名时使用cn()函数

### 4. AI协作规范
- ✅ **使用中文交流**：所有常规交互响应使用中文
- ✅ **多方案思考**：每个问题提供≥2个正交解决方案
- ✅ **自动决策执行**：AI自动选择最优方案并执行，用户可随时纠错
- ✅ **结构化思考**：复杂问题使用sequential-thinking工具分析

## 📋 开发检查清单

### 开始开发前
- [ ] 阅读了相关设计规范文档
- [ ] 确认了MCP工具配置正常
- [ ] 分析了现有组件，避免重复开发
- [ ] 确定了组件分类（ui/common-custom/project-custom）

### 组件开发中
- [ ] 使用了TypeScript类型定义
- [ ] 添加了完整的JSDoc注释
- [ ] 支持className属性
- [ ] 使用cn()函数处理类名
- [ ] 遵循了命名规范
- [ ] 考虑了无障碍性要求

### 组件开发完成
- [ ] 创建了示例页面
- [ ] 至少包含3个使用示例
- [ ] 通过了TypeScript检查
- [ ] 通过了ESLint检查
- [ ] 支持暗色模式
- [ ] 响应式设计正确

### 文档和测试
- [ ] 更新了组件清单
- [ ] 创建了API文档
- [ ] 添加了"何时使用"说明
- [ ] 配置了导航链接
- [ ] 编写了必要的测试

## 🛠️ MCP工具使用要点

### 文件操作
```javascript
// ✅ 批量读取文件
read_multiple_files_filesystem({
  paths: ["file1.tsx", "file2.tsx"]
})

// ✅ 使用正则搜索
view({
  path: "component.tsx",
  type: "file",
  search_query_regex: "interface.*Props"
})
```

### 结构化思考
```javascript
// ✅ 复杂问题分析
sequentialthinking_sequential_thinking({
  thought: "分析问题的核心要点",
  nextThoughtNeeded: true,
  thoughtNumber: 1,
  totalThoughts: 3
})
```

### 用户确认
```javascript
// ✅ 重要操作前确认
collect_feedback_feedback_enhanced({
  work_summary: "## 操作说明\n详细的操作描述..."
})
```

### 记忆存储
```javascript
// ✅ 记录重要信息
create_entities_memory({
  entities: [{
    name: "project-decision",
    entityType: "decision",
    observations: ["决策内容", "影响范围"]
  }]
})
```

## 🎨 样式规范要点

### 色彩使用
```tsx
// ✅ 使用语义化变量
<div className="bg-primary text-primary-foreground">

// ❌ 避免硬编码颜色
<div className="bg-blue-500 text-white">
```

### 类名处理
```tsx
// ✅ 使用cn函数
<div className={cn("base-class", className)}>

// ❌ 直接拼接字符串
<div className={`base-class ${className}`}>
```

### 响应式设计
```tsx
// ✅ 移动优先
<div className="w-full md:w-1/2 lg:w-1/3">

// ✅ 合理的断点使用
<div className="text-sm md:text-base lg:text-lg">
```

## 📝 TypeScript规范要点

### Props接口
```tsx
// ✅ 完整的Props定义
export interface ComponentProps {
  /**
   * 组件标题
   */
  title: string
  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean
  /**
   * 自定义类名
   */
  className?: string
}
```

### 组件导出
```tsx
// ✅ 具名导出
export function Component(props: ComponentProps) {
  // 组件实现
}

// ✅ 类型一起导出
export type { ComponentProps }
```

## ⚠️ 常见错误避免

### 开发错误
- ❌ 不使用MCP工具进行文件操作
- ❌ 硬编码颜色值
- ❌ 缺少TypeScript类型定义
- ❌ 不支持className属性
- ❌ 忽略暗色模式支持

### 协作错误
- ❌ 重要操作不确认用户
- ❌ 不记录重要决策
- ❌ 不使用结构化思考
- ❌ 忽略批量操作机会

### 文档错误
- ❌ 缺少组件示例
- ❌ 没有API文档
- ❌ 不更新导航配置
- ❌ 缺少使用说明

## 🚀 效率提升技巧

### 开发效率
1. **复用现有组件**：优先查看组件清单
2. **使用模板**：基于现有模板快速开发
3. **批量操作**：一次性处理多个相关文件
4. **工具组合**：合理组合MCP工具使用

### 质量保证
1. **代码检查**：使用ESLint和TypeScript检查
2. **样式一致**：遵循设计系统规范
3. **测试覆盖**：编写必要的测试用例
4. **文档同步**：保持文档与代码同步

### 协作效率
1. **明确需求**：开始前确保需求清晰
2. **及时反馈**：关键节点获取用户反馈
3. **记录决策**：使用memory工具记录重要信息
4. **分步执行**：复杂任务分解为多个步骤

## 📞 快速参考

### 按分类查找
- **通用规范**：`universal-*` 文档（前后端通用）
- **前端规范**：`frontend-*` 文档（前端专用）
- **项目规范**：`project-*` 文档（项目特定）

### 核心文档
- **核心指导**：[GUIDE.md](./GUIDE.md) - 50字总结规范
- **AI协作**：[universal-ai-collaboration.md](./universal-ai-collaboration.md)
- **MCP工具**：[universal-mcp-tools.md](./universal-mcp-tools.md)
- **组件开发**：[frontend-component-standards.md](./frontend-component-standards.md)
- **API规范**：[universal-api-standards.md](./universal-api-standards.md)

---

**记住**：始终优先使用MCP工具，遵循设计规范，确保代码质量！
