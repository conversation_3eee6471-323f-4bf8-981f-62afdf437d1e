# API接口规范

本文档规范了项目中API请求的实现方式、最佳实践和错误处理，确保代码一致性和可维护性。

## 📁 目录结构

```
services/
├── api/           # API请求接口
│   ├── userRequestApi.ts
│   ├── teamRequestApi.ts
│   └── ...
├── mock/          # 模拟数据
    ├── userMock.ts
    ├── teamMock.ts
    └── ...
```

## 🛠️ 请求工具库

项目中的请求工具库基于axios实现，提供了更加便捷的请求方式和统一的错误处理机制。

### 主要功能

- 请求和响应拦截
- 统一的错误处理
- 请求取消支持
- 类型安全的API定义
- 全局加载状态管理
- 统一的请求状态反馈

## 📝 基本使用方式

### 核心请求方法

```typescript
import { request } from '@/lib/request';

// GET 请求
const getData = async () => {
  const result = await request.get('/api/data');
  return result;
};

// 带参数的 GET 请求
const getUserById = async (id: number) => {
  // 方式一：使用params选项
  const user1 = await request.get('/api/users', { params: { id } });
  
  // 方式二：直接传递参数对象（会自动转换为params）
  const user2 = await request.get('/api/users', { id });
  
  return user1;
};

// POST 请求
const createData = async (data: any) => {
  const result = await request.post('/api/data', data);
  return result;
};

// PUT 请求
const updateData = async (id: number, data: any) => {
  const result = await request.put(`/api/data/${id}`, data);
  return result;
};

// DELETE 请求
const deleteData = async (id: number) => {
  await request.delete('/api/data', { id });
};
```

### 复杂请求配置

```typescript
const response = await request('/api/data', null, {
  method: 'get',
  params: { id: 1 },
  timeout: 5000,
  headers: {
    'X-Custom-Header': 'custom-value'
  },
  showLoading: true,
  loadingText: '加载中...',
  showErrorMessage: true,
  errorMessage: '请求失败',
  showSuccessMessage: false
});
```

## 🏗️ API服务层规范

### 命名规范

- **文件名**：`xxxRequestApi.ts`，如`userRequestApi.ts`
- **方法名**：`xxxRequest`，如`getUserRequest`、`createUserRequest`
- **一旦接口更换为真实接口，应删除对应的mock数据和mock接口**

### API定义最佳实践

```typescript
// services/api/userRequestApi.ts
import { request } from '@/lib/request';
import { ApiResponse } from '@/types/api';
import { User, CreateUserParams } from '@/types/user';

// 获取用户列表
export const getUsersRequest = async (): Promise<User[]> => {
  try {
    const response = await request.get<ApiResponse<User[]>>('/api/users');
    return response.data || [];
  } catch (error) {
    console.error('获取用户列表失败:', error);
    throw error;
  }
};

// 创建用户
export const createUserRequest = async (userData: CreateUserParams): Promise<User | null> => {
  try {
    const response = await request.post<ApiResponse<User>>('/api/users', userData);
    return response.data || null;
  } catch (error) {
    console.error('创建用户失败:', error);
    throw error;
  }
};

// 更新用户
export const updateUserRequest = async (id: string, userData: Partial<User>): Promise<boolean> => {
  try {
    const response = await request.put<ApiResponse<boolean>>(`/api/users/${id}`, userData);
    return !!response.data;
  } catch (error) {
    console.error('更新用户失败:', error);
    throw error;
  }
};

// 删除用户
export const deleteUserRequest = async (id: string): Promise<boolean> => {
  try {
    const response = await request.delete<ApiResponse<boolean>>(`/api/users/${id}`);
    return !!response.data;
  } catch (error) {
    console.error('删除用户失败:', error);
    throw error;
  }
};
```

## 🎭 Mock数据规范

```typescript
// services/mock/userMock.ts
import { User } from '@/types/user';

export const usersMock: User[] = [
  {
    id: '1',
    name: '张三',
    email: '<EMAIL>',
    role: 'admin'
  },
  {
    id: '2',
    name: '李四',
    email: '<EMAIL>',
    role: 'user'
  }
];
```

## 🔄 状态管理与反馈

### 加载状态

```typescript
// 显示加载状态
const getDataWithLoading = async () => {
  const result = await request.get('/api/data', {
    showLoading: true,
    loadingText: '加载数据中...'
  });
  return result;
};
```

### 成功反馈

```typescript
// 显示成功消息
const createDataWithSuccess = async (data: any) => {
  const result = await request.post('/api/data', data, {
    showSuccessMessage: true,
    successMessage: '创建成功！'
  });
  return result;
};
```

### 错误处理

```typescript
// 自定义错误消息
const fetchDataWithErrorHandling = async () => {
  try {
    const result = await request.get('/api/data', {
      showErrorMessage: true,
      errorMessage: '获取数据失败，请稍后重试'
    });
    return result;
  } catch (error) {
    // 错误已被自动处理，无需额外处理
    return null;
  }
};
```

## 🔐 认证与拦截器

### 认证拦截器

```typescript
// 请求拦截器
axios.interceptors.request.use((config: AxiosRequestConfig) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers = config.headers || {};
    config.headers['Authorization'] = `Bearer ${token}`;
  }
  return config;
});

// 响应拦截器
axios.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config;
    
    // 如果是401错误，尝试刷新token
    if (error.response?.status === 401 && originalRequest) {
      try {
        const newToken = await refreshToken();
        if (newToken && originalRequest.headers) {
          originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
          return axios(originalRequest);
        }
      } catch (refreshError) {
        // token刷新失败，重定向到登录页
        localStorage.removeItem('token');
        window.location.href = '/auth/login';
      }
    }
    
    return Promise.reject(error);
  }
);
```

## 🚀 实用工具与技巧

### 并发请求

```typescript
// 并发请求
const fetchMultipleResources = async () => {
  try {
    const [users, teams, projects] = await Promise.all([
      request.get('/api/users'),
      request.get('/api/teams'),
      request.get('/api/projects')
    ]);
    
    return { users, teams, projects };
  } catch (error) {
    console.error('获取资源失败:', error);
    return null;
  }
};
```

### 请求重试

```typescript
// 请求重试
const fetchWithRetry = async (url: string, maxRetries = 3) => {
  let retries = 0;
  
  while (retries < maxRetries) {
    try {
      return await request.get(url);
    } catch (error) {
      retries++;
      
      if (retries >= maxRetries) {
        throw error;
      }
      
      // 指数退避策略
      const delay = Math.pow(2, retries) * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
};
```

## 📋 最佳实践

1. **集中管理API请求**：所有API请求方法应在`services/api`目录下集中管理
2. **类型安全**：使用TypeScript类型确保请求参数和响应数据的类型安全
3. **错误处理**：每个API请求方法都应包含适当的错误处理
4. **Mock数据**：开发阶段使用mock数据，一旦真实API可用立即切换
5. **请求状态反馈**：为用户提供适当的加载、成功和错误状态反馈
6. **权限处理**：使用拦截器统一处理认证和授权
7. **避免重复请求**：合理使用缓存和防抖/节流技术
8. **明确参数命名**：API请求参数命名应清晰明确，与后端API文档保持一致

## 📝 类型定义示例

```typescript
// types/api.ts
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

// types/user.ts
export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user';
  createdAt: string;
  updatedAt: string;
}

export interface CreateUserParams {
  name: string;
  email: string;
  role: 'admin' | 'user';
}
```
