"use client"

import { <PERSON>actNode, useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Loader2 } from "lucide-react"

export interface FormField {
  id: string
  label: string
  type: 'text' | 'email' | 'password' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'custom'
  placeholder?: string
  required?: boolean
  options?: Array<{ label: string; value: string }>
  defaultValue?: string | boolean | number
  helpText?: string
  readOnly?: boolean
  disabled?: boolean
  validator?: (value: any) => string | null // 返回错误消息或null
  autoGenerate?: boolean // 是否支持自动生成
  autoGenerateHandler?: () => string | Promise<string> // 自动生成处理函数
  render?: (props: {
    id: string;
    value: any;
    onChange: (value: any) => void;
    error?: string;
  }) => ReactNode // 自定义渲染函数
}

export interface FormDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  description?: string
  fields: FormField[]
  onSubmit: (values: Record<string, any>) => Promise<void>
  submitButtonText?: string
  cancelButtonText?: string
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full'
  initialValues?: Record<string, any>
  children?: ReactNode
}

export default function FormDialog({
  open,
  onOpenChange,
  title,
  description,
  fields,
  onSubmit,
  submitButtonText = "确认",
  cancelButtonText = "取消",
  maxWidth = "sm",
  initialValues = {},
  children
}: FormDialogProps) {
  // 表单状态
  const [values, setValues] = useState<Record<string, any>>({...initialValues})
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  // 获取最大宽度类名
  const getMaxWidthClass = () => {
    switch (maxWidth) {
      case 'xs':
        return 'sm:max-w-[320px]'
      case 'sm':
        return 'sm:max-w-[425px]'
      case 'md':
        return 'sm:max-w-[640px]'
      case 'lg':
        return 'sm:max-w-[768px]'
      case 'xl':
        return 'sm:max-w-[1024px]'
      case 'full':
        return 'w-[calc(100vw-2rem)] max-w-[calc(100vw-2rem)]'
      default:
        return 'sm:max-w-[425px]'
    }
  }
  
  // 处理字段值变更
  const handleValueChange = (fieldId: string, value: any) => {
    setValues(prev => ({
      ...prev,
      [fieldId]: value
    }))
    
    // 清除错误
    if (errors[fieldId]) {
      setErrors(prev => {
        const newErrors = {...prev}
        delete newErrors[fieldId]
        return newErrors
      })
    }
  }
  
  // 处理自动生成
  const handleAutoGenerate = async (field: FormField) => {
    if (!field.autoGenerateHandler) return
    
    try {
      const generatedValue = await field.autoGenerateHandler()
      handleValueChange(field.id, generatedValue)
    } catch (error) {
      console.error(`自动生成字段 ${field.id} 值失败:`, error)
    }
  }
  
  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}
    let isValid = true
    
    fields.forEach(field => {
      // 必填验证
      if (field.required && (values[field.id] === undefined || values[field.id] === null || values[field.id] === '')) {
        newErrors[field.id] = `${field.label}不能为空`
        isValid = false
      }
      
      // 自定义验证
      if (field.validator && values[field.id] !== undefined) {
        const errorMessage = field.validator(values[field.id])
        if (errorMessage) {
          newErrors[field.id] = errorMessage
          isValid = false
        }
      }
    })
    
    setErrors(newErrors)
    return isValid
  }
  
  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // 表单验证
    if (!validateForm()) {
      return
    }
    
    setIsSubmitting(true)
    
    try {
      await onSubmit(values)
      // 提交成功后关闭对话框
      onOpenChange(false)
      // 重置表单
      setValues({...initialValues})
      setErrors({})
    } catch (error) {
      console.error('表单提交失败:', error)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  // 渲染表单字段
  const renderField = (field: FormField) => {
    const value = values[field.id] !== undefined ? values[field.id] : (field.defaultValue || '')
    const error = errors[field.id]
    
    // 自定义渲染
    if (field.type === 'custom' && field.render) {
      return (
        <div key={field.id} className="space-y-2">
          <div className="flex items-center justify-between">
            <label htmlFor={field.id} className="text-sm font-medium">
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </label>
          </div>
          {field.render({
            id: field.id,
            value,
            onChange: (newValue) => handleValueChange(field.id, newValue),
            error
          })}
          {field.helpText && !error && (
            <p className="text-xs text-muted-foreground">{field.helpText}</p>
          )}
          {error && <p className="text-xs text-destructive">{error}</p>}
        </div>
      )
    }
    
    // 文本输入
    if (field.type === 'text' || field.type === 'email' || field.type === 'password') {
      return (
        <div key={field.id} className="space-y-2">
          <div className="flex items-center justify-between">
            <label htmlFor={field.id} className="text-sm font-medium">
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </label>
          </div>
          <div className={field.autoGenerate ? "flex gap-2" : ""}>
            <input
              id={field.id}
              type={field.type}
              value={value as string}
              onChange={(e) => handleValueChange(field.id, e.target.value)}
              placeholder={field.placeholder}
              readOnly={field.readOnly}
              disabled={field.disabled || isSubmitting}
              className="w-full px-3 py-2 border border-input/30 bg-background/50 ring-1 ring-border/5 hover:border-input/50 transition-colors focus-visible:border-ring rounded-md"
            />
            {field.autoGenerate && (
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => handleAutoGenerate(field)}
                disabled={isSubmitting}
                className="cursor-pointer shrink-0 h-9"
              >
                生成
              </Button>
            )}
          </div>
          {field.helpText && !error && (
            <p className="text-xs text-muted-foreground">{field.helpText}</p>
          )}
          {error && <p className="text-xs text-destructive">{error}</p>}
        </div>
      )
    }
    
    // 文本区域
    if (field.type === 'textarea') {
      return (
        <div key={field.id} className="space-y-2">
          <div className="flex items-center justify-between">
            <label htmlFor={field.id} className="text-sm font-medium">
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </label>
          </div>
          <textarea
            id={field.id}
            value={value as string}
            onChange={(e) => handleValueChange(field.id, e.target.value)}
            placeholder={field.placeholder}
            readOnly={field.readOnly}
            disabled={field.disabled || isSubmitting}
            rows={4}
            className="w-full px-3 py-2 border border-input/30 bg-background/50 ring-1 ring-border/5 hover:border-input/50 transition-colors focus-visible:border-ring rounded-md resize-none"
          />
          {field.helpText && !error && (
            <p className="text-xs text-muted-foreground">{field.helpText}</p>
          )}
          {error && <p className="text-xs text-destructive">{error}</p>}
        </div>
      )
    }
    
    // 选择框
    if (field.type === 'select' && field.options) {
      return (
        <div key={field.id} className="space-y-2">
          <div className="flex items-center justify-between">
            <label htmlFor={field.id} className="text-sm font-medium">
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </label>
          </div>
          <select
            id={field.id}
            value={value as string}
            onChange={(e) => handleValueChange(field.id, e.target.value)}
            disabled={field.disabled || isSubmitting}
            className="w-full px-3 py-2 border border-input/30 bg-background/50 ring-1 ring-border/5 hover:border-input/50 transition-colors focus-visible:border-ring rounded-md"
          >
            <option value="">请选择{field.label}</option>
            {field.options.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          {field.helpText && !error && (
            <p className="text-xs text-muted-foreground">{field.helpText}</p>
          )}
          {error && <p className="text-xs text-destructive">{error}</p>}
        </div>
      )
    }
    
    // 复选框
    if (field.type === 'checkbox') {
      return (
        <div key={field.id} className="space-y-2">
          <div className="flex items-center gap-2">
            <input
              id={field.id}
              type="checkbox"
              checked={!!value}
              onChange={(e) => handleValueChange(field.id, e.target.checked)}
              disabled={field.disabled || isSubmitting}
              className="h-4 w-4 border border-input/30 bg-background/50 ring-1 ring-border/5 hover:border-input/50 transition-colors focus-visible:border-ring rounded"
            />
            <label htmlFor={field.id} className="text-sm font-medium">
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </label>
          </div>
          {field.helpText && !error && (
            <p className="text-xs text-muted-foreground ml-6">{field.helpText}</p>
          )}
          {error && <p className="text-xs text-destructive ml-6">{error}</p>}
        </div>
      )
    }
    
    // 单选框组
    if (field.type === 'radio' && field.options) {
      return (
        <div key={field.id} className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </span>
          </div>
          <div className="space-y-2">
            {field.options.map(option => (
              <div key={option.value} className="flex items-center gap-2">
                <input
                  id={`${field.id}-${option.value}`}
                  type="radio"
                  name={field.id}
                  value={option.value}
                  checked={value === option.value}
                  onChange={() => handleValueChange(field.id, option.value)}
                  disabled={field.disabled || isSubmitting}
                  className="h-4 w-4 border border-input/30 bg-background/50 ring-1 ring-border/5 hover:border-input/50 transition-colors focus-visible:border-ring"
                />
                <label htmlFor={`${field.id}-${option.value}`} className="text-sm">
                  {option.label}
                </label>
              </div>
            ))}
          </div>
          {field.helpText && !error && (
            <p className="text-xs text-muted-foreground">{field.helpText}</p>
          )}
          {error && <p className="text-xs text-destructive">{error}</p>}
        </div>
      )
    }
    
    return null
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={`${getMaxWidthClass()} border border-border shadow-md`}>
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold">{title}</DialogTitle>
          {description && (
            <DialogDescription className="text-sm text-muted-foreground mt-1">
              {description}
            </DialogDescription>
          )}
        </DialogHeader>
        
        <form onSubmit={handleSubmit}>
          <div className="grid gap-5 py-5">
            {fields.map(renderField)}
            {children}
          </div>
          
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              {cancelButtonText}
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {submitButtonText}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
} 