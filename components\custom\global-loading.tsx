"use client"

import {useEffect, useState} from "react"
import {Progress} from "@/components/ui/progress"

// 创建一个全局加载状态管理对象
const GlobalLoadingState = {
  listeners: new Set<(isLoading: boolean, text?: string) => void>(),
  isLoading: false,
  loadingText: "",
  
  subscribe(callback: (isLoading: boolean, text?: string) => void) {
    this.listeners.add(callback);
    return () => {
      this.listeners.delete(callback);
    };
  },
  
  setLoading(isLoading: boolean, text?: string) {
    this.isLoading = isLoading;
    this.loadingText = text || "";
    this.listeners.forEach(callback => callback(isLoading, text));
  }
};

// 导出加载状态方法供request/utils.ts使用
export const globalLoading = {
  show: (text?: string) => {
    GlobalLoadingState.setLoading(true, text);
  },
  
  hide: () => {
    GlobalLoadingState.setLoading(false);
  }
};

export function GlobalLoading() {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingText, setLoadingText] = useState("");
  
  useEffect(() => {
    return GlobalLoadingState.subscribe((loading, text) => {
      setIsLoading(loading);
      setLoadingText(text || "");
    });
  }, []);
  
  if (!isLoading) return null;
  
  return (
    <div className="fixed top-0 left-0 right-0 z-50">
      <Progress value={100} className="h-1 w-full animate-pulse" />
      {loadingText && (
        <div className="absolute top-2 right-4 text-xs font-medium text-muted-foreground bg-background px-2 py-1 rounded-md shadow-sm">
          {loadingText}
        </div>
      )}
    </div>
  );
} 