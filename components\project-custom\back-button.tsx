"use client"

import * as React from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { BackButton as CommonBackButton, BackButtonProps as CommonBackButtonProps } from "@/components/common-custom/back-button"

// ============================================================================
// 项目特定返回按钮组件 - 集成 Next.js 路由
// ============================================================================

/**
 * 项目特定的返回按钮属性
 * 扩展通用组件属性，添加 Next.js 特定功能
 */
export interface ProjectBackButtonProps extends Omit<CommonBackButtonProps, 'onNavigate'> {
  /**
   * 是否使用 Next.js Link 组件
   * @default true
   */
  useLink?: boolean

  /**
   * Next.js Link 组件的额外属性
   */
  linkProps?: React.ComponentProps<typeof Link>

  /**
   * 自定义导航处理函数（覆盖默认的 router.push）
   */
  onNavigate?: (href: string, e: React.MouseEvent) => void
}

/**
 * 项目特定的返回按钮组件
 * 
 * 基于通用返回按钮组件，集成了 Next.js 的路由功能：
 * - 默认使用 Next.js Link 组件进行导航
 * - 支持 useRouter 进行程序化导航
 * - 保持与通用组件的完全兼容性
 * 
 * @example
 * ```tsx
 * // 使用 Link 组件（默认）
 * <ProjectBackButton href="/dashboard" showText>返回仪表板</ProjectBackButton>
 * 
 * // 使用程序化导航
 * <ProjectBackButton 
 *   href="/users" 
 *   useLink={false}
 *   showText
 * >
 *   返回用户列表
 * </ProjectBackButton>
 * 
 * // 自定义导航逻辑
 * <ProjectBackButton 
 *   href="/custom" 
 *   onNavigate={(href) => {
 *     // 自定义导航逻辑
 *     console.log('Navigating to:', href);
 *     router.push(href);
 *   }}
 * />
 * ```
 */
export function ProjectBackButton({
  href,
  useLink = true,
  linkProps,
  onNavigate,
  ...props
}: ProjectBackButtonProps) {
  const router = useRouter()

  // 默认导航处理函数
  const handleNavigate = React.useCallback((targetHref: string, e: React.MouseEvent) => {
    if (onNavigate) {
      onNavigate(targetHref, e);
    } else {
      e.preventDefault();
      router.push(targetHref);
    }
  }, [router, onNavigate]);

  // 如果使用 Link 组件且有 href
  if (useLink && href) {
    return (
      <Link
        href={href}
        {...linkProps}
        className={props.className}
      >
        <CommonBackButton
          {...props}
          href={undefined} // 移除 href，因为由 Link 处理
          className={undefined} // 移除 className，因为由 Link 处理
        />
      </Link>
    )
  }

  // 使用通用组件的按钮模式
  return (
    <CommonBackButton
      {...props}
      href={href}
      onNavigate={href ? handleNavigate : undefined}
    />
  )
}

/**
 * 兼容性导出 - 保持与现有代码的兼容性
 * 这个组件与当前项目中的 BackButton 接口兼容
 */
export function BackButton({
  href,
  className,
  children,
  showText = false
}: {
  href: string
  className?: string
  children?: React.ReactNode
  showText?: boolean
}) {
  return (
    <ProjectBackButton
      href={href}
      className={className}
      showText={showText}
      useLink={true}
    >
      {children}
    </ProjectBackButton>
  )
}

// 导出类型
export type { ProjectBackButtonProps }
export type { BackButtonProps as CommonBackButtonProps } from "@/components/common-custom/back-button"
