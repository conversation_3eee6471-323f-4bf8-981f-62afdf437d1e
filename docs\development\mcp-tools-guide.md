# MCP工具使用指南

本文档详细说明了如何合理、高效地使用MCP（Model Context Protocol）工具集来解决问题和实现功能。

## 📋 已配置的MCP服务器

当前项目已配置以下MCP服务器：

| 服务器名称 | 包名 | 版本 | 主要功能 |
|-----------|------|------|----------|
| context7 | @upstash/context7-mcp | v1.0.14 | 代码上下文检索和分析 |
| filesystem | @modelcontextprotocol/server-filesystem | latest | 本地文件系统操作 |
| sequential-thinking | @modelcontextprotocol/server-sequential-thinking | 0.6.2 | 结构化思考和推理 |
| fetch | fetch-mcp | 0.0.5 | 网络请求和内容获取 |
| playwright | @executeautomation/playwright-mcp-server | 1.0.6 | 浏览器自动化和测试 |
| memory | @modelcontextprotocol/server-memory | 2025.4.25 | 持久化记忆存储 |
| feedback-enhanced | mcp-feedback-collector-md | 2.2.1 | 用户交互和反馈收集 |

## 🎯 核心原则

在解决问题或实现功能时，应该合理、高效地使用MCP工具，遵循以下原则：

### 1. 工具优先级原则
- **优先使用专门的MCP工具**，而不是通用方法
- **组合使用多个工具**以实现复杂功能
- **避免重复造轮子**，充分利用现有工具能力

### 2. 效率最大化原则
- **批量操作优于单次操作**
- **并行处理优于串行处理**
- **缓存结果优于重复计算**

### 3. 用户体验原则
- **重要操作前必须确认**
- **提供清晰的进度反馈**
- **支持操作撤销和回滚**

## 🛠️ 各工具详细使用规范

### context7 - 代码上下文检索
**核心功能：**
- 智能代码搜索和上下文分析
- 相关代码片段检索
- 代码库结构理解

**使用场景：**
- 查找相似的组件实现
- 理解代码库架构和模式
- 获取API使用示例
- 分析代码依赖关系

**最佳实践：**
```javascript
// ✅ 正确：查找相关组件实现
resolve_library_id("react button component")
get_library_docs("/facebook/react", {
  topic: "button components",
  tokens: 5000
})
```

### filesystem - 文件系统操作
**核心功能：**
- 本地文件和目录的完整操作
- 文件内容搜索和分析
- 目录结构管理

**使用场景：**
- 读取、写入、编辑本地文件
- 目录结构分析和管理
- 文件内容搜索和检索
- 批量文件操作

**最佳实践：**
```javascript
// ✅ 正确：批量读取相关文件
read_multiple_files_filesystem({
  paths: [
    "src/components/Button.tsx",
    "src/components/Input.tsx",
    "src/styles/components.css"
  ]
})

// ✅ 正确：使用正则搜索文件内容
view({
  path: "src/components/Button.tsx",
  type: "file",
  search_query_regex: "interface.*Props",
  case_sensitive: false
})
```

### sequential-thinking - 结构化思考
**核心功能：**
- 复杂问题的结构化分析
- 多步骤解决方案规划
- 逻辑推理和决策支持

**使用场景：**
- 复杂技术问题分析
- 架构设计决策
- 多步骤实施计划
- 代码重构策略

**最佳实践：**
```javascript
// ✅ 正确：用于复杂问题分解
sequentialthinking_sequential_thinking({
  thought: "需要设计一个可扩展的组件库架构，首先分析现有组件结构",
  nextThoughtNeeded: true,
  thoughtNumber: 1,
  totalThoughts: 5
})
```

### memory - 持久化记忆存储
**核心功能：**
- 知识图谱构建和管理
- 实体关系存储
- 长期记忆保持

**使用场景：**
- 存储项目相关信息和配置
- 记录用户偏好和习惯
- 缓存重要的分析结果
- 构建代码库知识图谱

**最佳实践：**
```javascript
// ✅ 正确：创建项目实体
create_entities_memory({
  entities: [{
    name: "web-template-demo",
    entityType: "project",
    observations: [
      "React组件库项目",
      "使用TypeScript和Tailwind CSS",
      "包含shadcn/ui基础组件"
    ]
  }]
})
```

### feedback-enhanced - 用户交互和反馈
**核心功能：**
- 用户反馈收集和展示
- 交互式确认和选择
- Markdown渲染和代码高亮

**使用场景：**
- 重要操作前的用户确认
- 收集用户偏好和选择
- 展示工作汇报和中间结果
- 代码审核和反馈收集

**最佳实践：**
```javascript
// ✅ 正确：重要操作前确认
collect_feedback_feedback_enhanced({
  work_summary: `
## 即将执行的操作

### 文件删除
- old-component.tsx
- unused-styles.css

### 影响范围
- 可能影响现有的导入引用
- 需要更新相关测试文件

请确认是否继续执行？
  `
})
```

## 🔄 工具组合使用模式

### 模式1：分析-思考-实施
```javascript
// 1. 分析现状
const files = filesystem.read_multiple_files(["src/**/*.tsx"])

// 2. 结构化思考
const plan = sequential_thinking({
  problem: "重构组件库",
  context: files
})

// 3. 用户确认
const approval = feedback_enhanced.request_feedback({
  message: "重构计划如下，是否继续？",
  details: plan
})

// 4. 执行实施
if (approval.confirmed) {
  filesystem.write_file("refactor-plan.md", plan)
}
```

### 模式2：研究-验证-应用
```javascript
// 1. 研究最佳实践
const docs = fetch.get("https://react.dev/learn/thinking-in-react")

// 2. 查找相关代码
const examples = context7.search({
  query: "react component patterns",
  type: "examples"
})

// 3. 记录发现
memory.store({
  key: "research_findings",
  value: { docs, examples }
})
```

## ⚠️ 注意事项

### 避免的反模式

1. **过度使用工具**
   - 不要为了使用工具而使用工具
   - 简单问题用简单方法解决

2. **忽略用户体验**
   - 重要操作必须使用feedback-enhanced确认
   - 提供清晰的操作说明和选项

3. **低效的工具组合**
   - 避免不必要的重复操作
   - 合理安排工具调用顺序

### 性能优化建议

1. **批量操作**
   - 使用filesystem的批量读写功能
   - 合并相关的网络请求

2. **缓存利用**
   - 使用memory存储常用数据
   - 避免重复的expensive操作

3. **并行处理**
   - 独立的操作可以并行执行
   - 合理利用异步特性

## 📋 检查清单

在使用MCP工具前，请检查：

- [ ] 是否选择了最合适的工具？
- [ ] 是否可以批量处理？
- [ ] 是否需要用户确认？
- [ ] 是否需要缓存结果？
- [ ] 是否有错误处理？
- [ ] 是否考虑了用户体验？

遵循这些规范，可以最大化MCP工具的效用，提供更好的开发体验和更高的工作效率。
