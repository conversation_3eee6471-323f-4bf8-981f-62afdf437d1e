"use client"

import * as React from "react"
import { Search, RefreshCw, LayoutGrid, Settings2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"
import type { TableFilter } from "./types"
import { useToast } from "@/components/ui/use-toast"

export interface DataTableToolbarProps<TData> {
  searchKey?: string
  searchValue: string
  onSearchChange: (value: string) => void
  filters?: TableFilter[]
  onFilterChange?: (key: string, value: any) => void
  renderFilter?: (filter: TableFilter) => React.ReactNode
  columns: any[]
  columnVisibility: Record<string, boolean>
  onColumnVisibilityChange: (visibility: Record<string, boolean>) => void
  density: "compact" | "default" | "comfortable"
  onDensityChange: (density: "compact" | "default" | "comfortable") => void
  onRefresh?: () => void
  actions?: React.ReactNode
  className?: string
}

export function DataTableToolbar<TData>({
  searchKey,
  searchValue,
  onSearchChange,
  filters = [],
  renderFilter,
  columns,
  columnVisibility,
  onColumnVisibilityChange,
  density,
  onDensityChange,
  onRefresh,
  actions,
  className,
}: DataTableToolbarProps<TData>) {
  const { toast } = useToast()
  const [isRefreshing, setIsRefreshing] = React.useState(false)

  const handleRefresh = React.useCallback(() => {
    if (onRefresh) {
      setIsRefreshing(true)
      
      // 模拟刷新操作
      setTimeout(() => {
        onRefresh()
        setIsRefreshing(false)
        toast({
          title: "刷新成功",
          description: "数据已更新"
        })
      }, 500)
    }
  }, [onRefresh, toast])

  return (
    <div className={cn("flex flex-wrap items-center justify-between gap-2 py-4", className)}>
      <div className="flex flex-1 items-center space-x-2">
        {searchKey && (
          <div className="relative w-full max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索..."
              value={searchValue}
              onChange={(event) => onSearchChange(event.target.value)}
              className="w-full pl-8 pr-4"
            />
          </div>
        )}
        
        {filters.length > 0 && renderFilter && (
          <div className="flex flex-wrap items-center gap-2">
            {filters.map(renderFilter)}
          </div>
        )}
      </div>
      
      <div className="flex items-center gap-2">
        {actions}
        
        {onRefresh && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="h-8 gap-1"
          >
            <RefreshCw className={cn("h-4 w-4", isRefreshing && "animate-spin")} />
            <span className="hidden sm:inline-block">刷新</span>
          </Button>
        )}
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-8 gap-1">
              <LayoutGrid className="h-4 w-4" />
              <span className="hidden sm:inline-block">密度</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-40">
            <DropdownMenuLabel>表格密度</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuCheckboxItem
              checked={density === "compact"}
              onCheckedChange={() => onDensityChange("compact")}
            >
              紧凑
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              checked={density === "default"}
              onCheckedChange={() => onDensityChange("default")}
            >
              默认
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              checked={density === "comfortable"}
              onCheckedChange={() => onDensityChange("comfortable")}
            >
              宽松
            </DropdownMenuCheckboxItem>
          </DropdownMenuContent>
        </DropdownMenu>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-8 gap-1">
              <Settings2 className="h-4 w-4" />
              <span className="hidden sm:inline-block">列设置</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuLabel>显示列</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {columns
              .filter((column) => column.accessorKey && column.header)
              .map((column) => {
                const header = typeof column.header === 'string' ? column.header : column.accessorKey;
                return (
                  <DropdownMenuCheckboxItem
                    key={column.accessorKey}
                    className="capitalize"
                    checked={columnVisibility[column.accessorKey]}
                    onCheckedChange={(checked) => {
                      onColumnVisibilityChange({
                        ...columnVisibility,
                        [column.accessorKey]: checked,
                      })
                    }}
                  >
                    {header}
                  </DropdownMenuCheckboxItem>
                )
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
} 