# 表单设计

表单是用户与应用交互的主要方式之一，良好的表单设计能够降低用户输入负担，提高数据收集质量。本文档规范了项目中表单的设计原则、布局规则和交互方式。

## 表单设计原则

### 1. 简洁明了

- 只收集必要的信息，减少表单字段数量
- 将复杂表单分解为多个逻辑步骤
- 使用清晰的分组和层次结构提高可扫描性

### 2. 引导式设计

- 按照用户思维逻辑排列字段
- 使用明确的标签和帮助文本减少误解
- 提供即时验证和反馈，引导用户正确完成

### 3. 减少认知负担

- 使用自动完成和智能默认值减少输入量
- 采用一致的表单样式和交互模式
- 在适当情况下使用单选按钮或下拉菜单代替自由文本输入

## 表单布局

### 标准表单布局

标准表单布局使用垂直排列的字段，适合大多数场景：

```jsx
import { Form, FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"

function StandardForm() {
  return (
    <Form>
      <form className="space-y-6">
        <FormField
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>姓名</FormLabel>
              <FormControl>
                <Input placeholder="请输入姓名" {...field} />
              </FormControl>
              <FormDescription>
                请输入您的真实姓名
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>电子邮箱</FormLabel>
              <FormControl>
                <Input placeholder="请输入电子邮箱" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        {/* 更多字段... */}
        
        <Button type="submit">提交</Button>
      </form>
    </Form>
  )
}
```

### 内联表单布局

适用于简单的表单，如搜索框或筛选器：

```jsx
function InlineForm() {
  return (
    <form className="flex space-x-2">
      <div className="flex-1">
        <Input placeholder="搜索..." />
      </div>
      <Button type="submit">搜索</Button>
    </form>
  )
}
```

### 分组表单布局

对复杂表单进行逻辑分组：

```jsx
function GroupedForm() {
  return (
    <Form>
      <form className="space-y-8">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">个人信息</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>名字</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>姓氏</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        
        <div className="space-y-4">
          <h3 className="text-lg font-medium">联系方式</h3>
          <div className="space-y-4">
            <FormField
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>电子邮箱</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>电话号码</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        
        <Button type="submit">保存信息</Button>
      </form>
    </Form>
  )
}
```

### 多步骤表单

将复杂表单分解为多个步骤，减少用户一次性面对的复杂度：

```jsx
import { useState } from "react"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"

function MultiStepForm() {
  const [step, setStep] = useState(1)
  const totalSteps = 3
  
  const nextStep = () => setStep(Math.min(step + 1, totalSteps))
  const prevStep = () => setStep(Math.max(step - 1, 1))
  
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>创建账户 - 步骤 {step}/{totalSteps}</CardTitle>
      </CardHeader>
      <CardContent>
        {step === 1 && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium">个人信息</h3>
            {/* 步骤1表单内容 */}
          </div>
        )}
        
        {step === 2 && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium">账户设置</h3>
            {/* 步骤2表单内容 */}
          </div>
        )}
        
        {step === 3 && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium">确认信息</h3>
            {/* 步骤3表单内容 */}
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={prevStep}
          disabled={step === 1}
        >
          上一步
        </Button>
        {step < totalSteps ? (
          <Button onClick={nextStep}>下一步</Button>
        ) : (
          <Button type="submit">完成</Button>
        )}
      </CardFooter>
    </Card>
  )
}
```

## 表单元素规范

### 文本输入框

```jsx
// 基本输入框
<FormItem>
  <FormLabel>用户名</FormLabel>
  <FormControl>
    <Input placeholder="请输入用户名" />
  </FormControl>
  <FormDescription>用户名长度为3-20个字符</FormDescription>
  <FormMessage />
</FormItem>

// 禁用状态
<Input disabled value="只读内容" />

// 错误状态
<Input 
  aria-invalid="true"
  className="border-destructive focus-visible:ring-destructive" 
/>

// 带图标输入框
<div className="relative">
  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
  <Input className="pl-8" placeholder="搜索..." />
</div>
```

### 选择框

```jsx
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

<FormItem>
  <FormLabel>角色</FormLabel>
  <Select>
    <SelectTrigger>
      <SelectValue placeholder="选择角色" />
    </SelectTrigger>
    <SelectContent>
      <SelectItem value="admin">管理员</SelectItem>
      <SelectItem value="user">普通用户</SelectItem>
      <SelectItem value="guest">访客</SelectItem>
    </SelectContent>
  </Select>
  <FormMessage />
</FormItem>
```

### 复选框和单选框

```jsx
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

// 复选框
<FormItem className="flex flex-row items-start space-x-3 space-y-0">
  <FormControl>
    <Checkbox />
  </FormControl>
  <div className="space-y-1 leading-none">
    <FormLabel>接收营销邮件</FormLabel>
    <FormDescription>
      我们会定期发送产品更新和优惠信息
    </FormDescription>
  </div>
</FormItem>

// 单选按钮组
<FormItem>
  <FormLabel>通知方式</FormLabel>
  <FormControl>
    <RadioGroup defaultValue="email">
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="email" id="r1" />
        <FormLabel htmlFor="r1" className="font-normal">
          电子邮件
        </FormLabel>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="sms" id="r2" />
        <FormLabel htmlFor="r2" className="font-normal">
          短信
        </FormLabel>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="push" id="r3" />
        <FormLabel htmlFor="r3" className="font-normal">
          推送通知
        </FormLabel>
      </div>
    </RadioGroup>
  </FormControl>
  <FormMessage />
</FormItem>
```

### 文本区域

```jsx
import { Textarea } from "@/components/ui/textarea"

<FormItem>
  <FormLabel>描述</FormLabel>
  <FormControl>
    <Textarea 
      placeholder="请输入描述内容"
      className="min-h-[120px]" 
    />
  </FormControl>
  <FormDescription>
    简要描述您的项目，最多500字
  </FormDescription>
  <FormMessage />
</FormItem>
```

### 日期选择器

```jsx
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

<FormItem className="flex flex-col">
  <FormLabel>出生日期</FormLabel>
  <Popover>
    <PopoverTrigger asChild>
      <FormControl>
        <Button
          variant="outline"
          className={cn(
            "w-full pl-3 text-left font-normal",
            !field.value && "text-muted-foreground"
          )}
        >
          {field.value ? (
            format(field.value, "yyyy年MM月dd日")
          ) : (
            <span>选择日期</span>
          )}
          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
        </Button>
      </FormControl>
    </PopoverTrigger>
    <PopoverContent className="w-auto p-0" align="start">
      <Calendar
        mode="single"
        selected={field.value}
        onSelect={field.onChange}
        disabled={(date) =>
          date > new Date() || date < new Date("1900-01-01")
        }
        initialFocus
      />
    </PopoverContent>
  </Popover>
  <FormMessage />
</FormItem>
```

## 表单验证

我们使用Zod进行类型安全的表单验证，结合react-hook-form处理表单状态。

### 验证规则示例

```jsx
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"

// 定义验证模式
const formSchema = z.object({
  username: z.string().min(3, {
    message: "用户名至少需要3个字符",
  }).max(20, {
    message: "用户名不能超过20个字符",
  }),
  email: z.string().email({
    message: "请输入有效的电子邮箱地址",
  }),
  password: z.string().min(8, {
    message: "密码至少需要8个字符",
  }).regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).*$/, {
    message: "密码需包含大小写字母和数字",
  }),
  confirmPassword: z.string(),
  terms: z.boolean().refine((val) => val === true, {
    message: "您必须同意服务条款",
  }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "两次输入的密码不一致",
  path: ["confirmPassword"],
})
```

### 完整表单验证示例

```jsx
function ValidatedForm() {
  // 初始化表单
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: "",
      email: "",
      password: "",
      confirmPassword: "",
      terms: false,
    },
  })
  
  // 提交处理
  function onSubmit(values) {
    console.log(values)
    // 处理表单提交
  }
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormLabel>用户名</FormLabel>
              <FormControl>
                <Input placeholder="请输入用户名" {...field} />
              </FormControl>
              <FormDescription>
                您的公开显示名称
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>电子邮箱</FormLabel>
              <FormControl>
                <Input placeholder="请输入电子邮箱" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>密码</FormLabel>
              <FormControl>
                <Input type="password" placeholder="请输入密码" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel>确认密码</FormLabel>
              <FormControl>
                <Input type="password" placeholder="请再次输入密码" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="terms"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
              <FormControl>
                <Checkbox 
                  checked={field.value} 
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>接受条款和条件</FormLabel>
                <FormDescription>
                  我已阅读并同意<a href="/terms" className="text-primary underline">服务条款</a>
                </FormDescription>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <Button type="submit">注册</Button>
      </form>
    </Form>
  )
}
```

## 表单交互反馈

### 实时验证

提供即时的表单验证反馈，帮助用户在提交前纠正错误。

```jsx
<FormField
  control={form.control}
  name="email"
  render={({ field }) => (
    <FormItem>
      <FormLabel>电子邮箱</FormLabel>
      <FormControl>
        <Input 
          placeholder="请输入电子邮箱" 
          {...field} 
          onBlur={(e) => {
            field.onBlur(e)
            form.trigger("email") // 失焦时触发验证
          }}
        />
      </FormControl>
      <FormMessage />
    </FormItem>
  )}
/>
```

### 加载状态

表单提交时显示加载状态，防止重复提交。

```jsx
function FormWithLoadingState() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  async function onSubmit(values) {
    setIsSubmitting(true)
    try {
      // 处理表单提交
      await submitForm(values)
      toast({
        title: "提交成功",
        description: "您的信息已成功保存",
      })
    } catch (error) {
      toast({
        variant: "destructive",
        title: "提交失败",
        description: error.message || "请稍后重试",
      })
    } finally {
      setIsSubmitting(false)
    }
  }
  
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      {/* 表单字段 */}
      <Button disabled={isSubmitting} type="submit">
        {isSubmitting ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            提交中...
          </>
        ) : (
          "提交"
        )}
      </Button>
    </form>
  )
}
```

### 表单提交反馈

使用toast组件提供表单提交结果的反馈。

```jsx
import { useToast } from "@/components/ui/use-toast"

function FormWithFeedback() {
  const { toast } = useToast()
  
  async function onSubmit(values) {
    try {
      // 处理表单提交
      await submitForm(values)
      toast({
        title: "提交成功",
        description: "您的信息已成功保存",
      })
      form.reset() // 成功后重置表单
    } catch (error) {
      toast({
        variant: "destructive",
        title: "提交失败",
        description: error.message || "请稍后重试",
      })
    }
  }
  
  // 表单实现...
}
```

## 表单无障碍性

确保表单对所有用户都可访问，包括使用辅助技术的用户。

### 无障碍标签

```jsx
// 使用明确的标签
<FormLabel htmlFor="email">电子邮箱</FormLabel>
<Input id="email" name="email" type="email" />

// 使用aria-labelledby关联
<div id="date-label">出生日期</div>
<Input aria-labelledby="date-label" type="date" />

// 使用aria-describedby提供额外描述
<FormLabel htmlFor="password">密码</FormLabel>
<Input 
  id="password" 
  type="password" 
  aria-describedby="password-requirements" 
/>
<div id="password-requirements" className="text-xs text-muted-foreground">
  密码须包含至少8个字符，包括大小写字母和数字
</div>
```

### 错误信息无障碍化

```jsx
<FormItem>
  <FormLabel htmlFor="username">用户名</FormLabel>
  <FormControl>
    <Input 
      id="username" 
      {...field} 
      aria-invalid={!!errors.username}
      aria-describedby={errors.username ? "username-error" : undefined}
    />
  </FormControl>
  {errors.username && (
    <FormMessage id="username-error" />
  )}
</FormItem>
```

## 表单响应式设计

表单应适应各种屏幕尺寸，确保在移动设备上同样易于使用。

### 响应式表单布局

```jsx
<div className="space-y-6">
  {/* 小屏幕单列，大屏幕双列 */}
  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    <FormField
      name="firstName"
      render={({ field }) => (
        <FormItem>
          <FormLabel>名字</FormLabel>
          <FormControl>
            <Input {...field} />
          </FormControl>
        </FormItem>
      )}
    />
    <FormField
      name="lastName"
      render={({ field }) => (
        <FormItem>
          <FormLabel>姓氏</FormLabel>
          <FormControl>
            <Input {...field} />
          </FormControl>
        </FormItem>
      )}
    />
  </div>
  
  {/* 始终单列的字段 */}
  <FormField
    name="address"
    render={({ field }) => (
      <FormItem>
        <FormLabel>地址</FormLabel>
        <FormControl>
          <Textarea {...field} />
        </FormControl>
      </FormItem>
    )}
  />
  
  {/* 移动设备上堆叠，桌面设备上内联的按钮 */}
  <div className="flex flex-col sm:flex-row sm:justify-end gap-2">
    <Button variant="outline">取消</Button>
    <Button type="submit">保存</Button>
  </div>
</div>
```

### 移动设备优化

```jsx
// 移动设备上更大的点击区域
<FormItem className="mobile-optimized">
  <FormLabel className="text-base">通知设置</FormLabel>
  <div className="mt-2">
    <div className="flex items-center py-3 border-b">
      <Checkbox id="emailNotify" className="h-5 w-5" />
      <label htmlFor="emailNotify" className="ml-3 flex-1 text-base">
        电子邮件通知
      </label>
    </div>
    <div className="flex items-center py-3 border-b">
      <Checkbox id="smsNotify" className="h-5 w-5" />
      <label htmlFor="smsNotify" className="ml-3 flex-1 text-base">
        短信通知
      </label>
    </div>
  </div>
</FormItem>

// CSS调整
<style jsx>{`
  @media (max-width: 640px) {
    /* 移动设备上更大的输入框 */
    input, textarea, select {
      font-size: 16px; /* 防止iOS缩放 */
      padding: 12px;
    }
    
    /* 更大的按钮 */
    button {
      padding: 12px 16px;
      width: 100%;
    }
  }
`}</style>
```

## 最佳实践

### 表单设计推荐做法

1. **使用明确的错误信息**：具体说明错误原因和如何修复，而不是简单的"无效输入"
2. **保存用户进度**：对于长表单，提供自动保存或明确的保存按钮
3. **提供默认值**：尽可能为字段提供智能默认值，减少用户输入
4. **优化键盘操作**：确保表单可通过Tab键顺序浏览，Enter键提交
5. **优先使用原生HTML5表单验证**：利用required、pattern等属性进行基本验证

### 表单设计避免事项

1. **避免过长表单**：用户可能因表单过长而放弃，考虑分步骤填写
2. **避免不必要的字段**：只收集真正需要的信息
3. **避免令人困惑的标签**：使用简单明了的词语，避免技术术语
4. **避免提前验证**：在用户完成输入前不要显示错误
5. **避免自动提交**：除非是单一字段的简单表单，否则需要明确的提交按钮

## 表单模板示例

### 登录表单

```jsx
function LoginForm() {
  const form = useForm({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  })
  
  function onSubmit(values) {
    // 处理登录
  }
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>电子邮箱</FormLabel>
              <FormControl>
                <Input placeholder="请输入电子邮箱" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <div className="flex items-center justify-between">
                <FormLabel>密码</FormLabel>
                <Link href="/forgot-password" className="text-sm text-primary">
                  忘记密码?
                </Link>
              </div>
              <FormControl>
                <Input type="password" placeholder="请输入密码" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="rememberMe"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center space-x-2 space-y-0">
              <FormControl>
                <Checkbox 
                  checked={field.value} 
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <FormLabel className="text-sm font-normal">记住我</FormLabel>
            </FormItem>
          )}
        />
        
        <Button type="submit" className="w-full">
          登录
        </Button>
      </form>
    </Form>
  )
}
```

### 用户资料表单

```jsx
function ProfileForm({ defaultValues }) {
  const form = useForm({
    resolver: zodResolver(profileSchema),
    defaultValues,
  })
  
  function onSubmit(values) {
    // 处理资料更新
  }
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="space-y-6">
          <h3 className="text-lg font-medium">个人资料</h3>
          
          <div className="flex items-center gap-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={defaultValues.avatarUrl} />
              <AvatarFallback>
                {defaultValues.name?.charAt(0) || "U"}
              </AvatarFallback>
            </Avatar>
            <Button variant="outline" size="sm">
              更换头像
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>姓名</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>用户名</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormDescription>
                    您的URL将是: example.com/@{field.value}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          
          <FormField
            control={form.control}
            name="bio"
            render={({ field }) => (
              <FormItem>
                <FormLabel>个人简介</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="介绍一下自己..." 
                    className="min-h-[120px]" 
                    {...field} 
                  />
                </FormControl>
                <FormDescription>
                  最多150个字符
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="space-y-6">
          <h3 className="text-lg font-medium">联系信息</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>电子邮箱</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>手机号码</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        
        <div className="flex justify-end">
          <Button type="submit">保存更改</Button>
        </div>
      </form>
    </Form>
  )
}
``` 