# 色彩系统

我们的色彩系统基于Shadcn UI的基础上进行了优化，采用了类似Notion的中性色彩风格，同时添加了柔和的功能色彩。整个系统使用CSS变量定义，便于在明暗主题间平滑过渡。

## 主色调：精简中性色系

Shadcn UI和Notion都使用精心设计的中性色调作为主色系，我们采用了10个精细的亮度级别，呈现出柔和、专业且不失亲和力的视觉效果。

```css
/* 在globals.css中定义 */
:root {
  /* 中性色调 */
  --slate-50: oklch(0.985 0 0);
  --slate-100: oklch(0.975 0 0);
  --slate-200: oklch(0.927 0 0);
  --slate-300: oklch(0.863 0 0);
  --slate-400: oklch(0.708 0 0);
  --slate-500: oklch(0.556 0 0);
  --slate-600: oklch(0.445 0 0);
  --slate-700: oklch(0.305 0 0);
  --slate-800: oklch(0.205 0 0);
  --slate-900: oklch(0.145 0 0);
  --slate-950: oklch(0.093 0 0);
}
```

### 语义化色彩映射

为确保一致性，我们将基础色彩映射到语义化变量：

```css
:root {
  /* 基础样式变量 */
  --background: oklch(0.985 0 0);
  --foreground: oklch(0.145 0 0);
  
  /* 卡片组件 */
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  
  /* 弹出层 */
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  
  /* 主要元素 */
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  
  /* 次要元素 */
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  
  /* 弱化元素 */
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  
  /* 强调元素 */
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  
  /* 状态色彩 */
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.985 0 0);
  
  /* 边框与表单 */
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  
  /* 圆角 */
  --radius: 0.625rem;
}
```

## 功能色彩系统

功能色彩用于表达不同状态和信息类别，采用Shadcn UI和Notion的柔和风格：

```css
:root {
  /* 成功状态 */
  --success-50: oklch(0.960 0.090 149.115 / 10%);
  --success-100: oklch(0.960 0.090 149.115 / 20%);
  --success-200: oklch(0.887 0.100 149.115 / 30%);
  --success-300: oklch(0.787 0.110 149.115 / 40%);
  --success-400: oklch(0.687 0.120 149.115 / 60%);
  --success-500: oklch(0.617 0.130 149.115);
  --success-600: oklch(0.517 0.130 149.115);
  --success-700: oklch(0.417 0.120 149.115);
  --success-800: oklch(0.317 0.110 149.115);
  --success-900: oklch(0.217 0.100 149.115);
  
  /* 警告状态 */
  --warning-50: oklch(0.960 0.080 83.593 / 10%);
  --warning-100: oklch(0.960 0.080 83.593 / 20%);
  --warning-200: oklch(0.926 0.100 83.593 / 30%);
  --warning-300: oklch(0.886 0.120 83.593 / 40%);
  --warning-400: oklch(0.836 0.140 83.593 / 60%);
  --warning-500: oklch(0.796 0.165 83.593);
  --warning-600: oklch(0.756 0.165 83.593);
  --warning-700: oklch(0.706 0.160 83.593);
  --warning-800: oklch(0.656 0.155 83.593);
  --warning-900: oklch(0.606 0.150 83.593);
  
  /* 错误状态 */
  --error-50: oklch(0.960 0.095 27.325 / 10%);
  --error-100: oklch(0.960 0.095 27.325 / 20%);
  --error-200: oklch(0.887 0.125 27.325 / 30%);
  --error-300: oklch(0.807 0.155 27.325 / 40%);
  --error-400: oklch(0.727 0.185 27.325 / 60%);
  --error-500: oklch(0.677 0.215 27.325);
  --error-600: oklch(0.627 0.235 27.325);
  --error-700: oklch(0.577 0.245 27.325);
  --error-800: oklch(0.527 0.235 27.325);
  --error-900: oklch(0.477 0.225 27.325);
  
  /* 信息状态 */
  --info-50: oklch(0.960 0.060 232.812 / 10%);
  --info-100: oklch(0.960 0.060 232.812 / 20%);
  --info-200: oklch(0.904 0.070 232.812 / 30%);
  --info-300: oklch(0.844 0.080 232.812 / 40%);
  --info-400: oklch(0.784 0.090 232.812 / 60%);
  --info-500: oklch(0.684 0.100 232.812);
  --info-600: oklch(0.584 0.121 232.812);
  --info-700: oklch(0.484 0.121 232.812);
  --info-800: oklch(0.384 0.120 232.812);
  --info-900: oklch(0.284 0.119 232.812);
}
```

### 功能色彩应用

| 色彩类型 | 应用场景 | 浅色模式 | 深色模式 |
|---------|---------|---------|---------|
| 成功色（绿） | 成功状态、完成操作 | `var(--success-600)` | `var(--success-500)` |
| 警告色（黄） | 警告信息、需注意 | `var(--warning-600)` | `var(--warning-500)` |
| 错误色（红） | 错误状态、危险操作 | `var(--error-600)` | `var(--error-500)` |
| 信息色（蓝） | 提示信息、进行中 | `var(--info-600)` | `var(--info-500)` |

## 暗色模式变量

Shadcn UI和Notion都提供了精心设计的暗色模式，我们采用类似的对比度控制策略：

```css
.dark {
  /* 基础样式变量 */
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  
  /* 卡片组件 */
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  
  /* 弹出层 */
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  
  /* 主要元素 */
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  
  /* 次要元素 */
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  
  /* 弱化元素 */
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  
  /* 强调元素 */
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  
  /* 状态色彩 */
  --destructive: oklch(0.704 0.191 22.216);
  --destructive-foreground: oklch(0.985 0 0);
  
  /* 边框与表单 */
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
}
```

## 色彩层次与对比度

为了确保视觉舒适度和可读性，我们遵循以下色彩层次规则：

1. **文字与背景对比度**：确保达到WCAG 2.1 AA级别标准（正常文本4.5:1，大号文本3:1）
2. **深色模式调整**：深色模式中提高背景亮度，降低文字亮度，减少眩光
3. **强调色使用**：强调色用于引导注意，但不超过页面25%面积
4. **渐变使用**：渐变从基础色到透明，避免强烈色彩冲突

## 色彩无障碍设计

我们的色彩系统遵循以下无障碍原则：

1. **不仅依赖颜色传达信息**：搭配图标、文字或形状区分状态和信息
2. **对比度保障**：文字对比度≥4.5:1（使用`text-foreground/90`控制透明度）
3. **色盲友好**：避免红绿搭配作为唯一区分手段
4. **明暗主题适配**：自动根据系统偏好切换明暗主题

## 色彩组合示例

### Notion风格组件

Notion风格组件通常采用极简的色彩搭配，以内容为中心：

```jsx
// Notion风格卡片
<div className="p-6 rounded-lg border border-border bg-card text-card-foreground">
  <h3 className="text-xl font-medium">笔记标题</h3>
  <p className="mt-2 text-muted-foreground">这是笔记的内容描述，采用柔和的次要文本颜色。</p>
  <div className="flex gap-2 mt-4">
    <span className="px-2 py-1 text-xs rounded-full bg-primary/10 text-primary">标签一</span>
    <span className="px-2 py-1 text-xs rounded-full bg-info-500/10 text-info-600 dark:text-info-500">标签二</span>
  </div>
</div>
```

### 状态色组合

用于表示不同状态的组件：

```jsx
// 成功状态卡片
<div className="p-4 rounded-md bg-success-50 border border-success-100 text-success-800 dark:bg-success-950 dark:border-success-900 dark:text-success-400">
  <div className="flex items-center">
    <CheckCircleIcon className="h-5 w-5 mr-2 text-success-600 dark:text-success-500" />
    <p>操作已成功完成</p>
  </div>
</div>

// 错误状态卡片
<div className="p-4 rounded-md bg-error-50 border border-error-100 text-error-800 dark:bg-error-950 dark:border-error-900 dark:text-error-400">
  <div className="flex items-center">
    <XCircleIcon className="h-5 w-5 mr-2 text-error-600 dark:text-error-500" />
    <p>操作失败，请重试</p>
  </div>
</div>
```

## 动态色彩应用

Shadcn UI和Notion都大量使用透明度变化来创建微妙的层次感：

```jsx
// 按钮样式
<button className="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md">
  主按钮
</button>

<button className="bg-secondary text-secondary-foreground hover:bg-secondary/80 px-4 py-2 rounded-md">
  次要按钮
</button>

// 悬浮卡片
<div className="p-6 rounded-lg border border-border bg-card hover:bg-card/95 hover:border-primary/20 transition-colors">
  <h3>悬浮效果卡片</h3>
  <p>鼠标悬浮时会有微妙的背景色和边框变化</p>
</div>

// 渐变背景
<div className="bg-gradient-to-r from-primary/5 to-primary/0 p-6 rounded-lg">
  <h3>微妙渐变背景</h3>
  <p>使用低透明度的渐变创造柔和效果</p>
</div>
```

## 色彩命名约定

我们的色彩命名遵循以下约定，与Shadcn UI保持一致：

1. **基础色名**: `--{color}-{lightness}`，如`--slate-500`
2. **语义化名称**: `--{role}`，如`--primary`, `--accent`
3. **组件专用色**: `--{component}-{role}`，如`--card-foreground`

## Notion风格的独特色彩

除了基础色彩系统外，我们还提供了一组Notion风格的特殊色彩，用于标签、高亮和分类：

```css
:root {
  /* Notion风格的特殊色彩 */
  --notion-red: oklch(0.677 0.215 27.325);
  --notion-orange: oklch(0.796 0.165 83.593);
  --notion-yellow: oklch(0.896 0.145 93.593);
  --notion-green: oklch(0.617 0.130 149.115);
  --notion-blue: oklch(0.584 0.121 232.812);
  --notion-purple: oklch(0.604 0.181 302.812);
  --notion-pink: oklch(0.654 0.191 352.216);
  --notion-brown: oklch(0.677 0.115 63.593);
  --notion-gray: oklch(0.756 0.025 83.593);
}
```

### Notion风格标签

```jsx
// Notion风格标签组件
<div className="flex flex-wrap gap-2">
  <span className="px-2 py-1 rounded-md text-xs bg-[var(--notion-red)/10] text-[var(--notion-red)]">红色</span>
  <span className="px-2 py-1 rounded-md text-xs bg-[var(--notion-orange)/10] text-[var(--notion-orange)]">橙色</span>
  <span className="px-2 py-1 rounded-md text-xs bg-[var(--notion-yellow)/10] text-[var(--notion-yellow)]">黄色</span>
  <span className="px-2 py-1 rounded-md text-xs bg-[var(--notion-green)/10] text-[var(--notion-green)]">绿色</span>
  <span className="px-2 py-1 rounded-md text-xs bg-[var(--notion-blue)/10] text-[var(--notion-blue)]">蓝色</span>
  <span className="px-2 py-1 rounded-md text-xs bg-[var(--notion-purple)/10] text-[var(--notion-purple)]">紫色</span>
  <span className="px-2 py-1 rounded-md text-xs bg-[var(--notion-pink)/10] text-[var(--notion-pink)]">粉色</span>
  <span className="px-2 py-1 rounded-md text-xs bg-[var(--notion-brown)/10] text-[var(--notion-brown)]">棕色</span>
  <span className="px-2 py-1 rounded-md text-xs bg-[var(--notion-gray)/10] text-[var(--notion-gray)]">灰色</span>
</div>
``` 