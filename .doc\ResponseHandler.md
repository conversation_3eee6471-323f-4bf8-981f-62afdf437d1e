# 响应处理工具

这个文件包含了处理API响应和异常的工具函数，旨在简化请求处理流程和统一响应处理逻辑。

## 主要功能

- 统一处理API响应格式
- 自动解析成功和失败状态
- 自动处理异常情况
- 提供友好的错误提示
- 支持响应格式自定义检查
- 支持失败重试机制

## 工具函数说明

### processResponse

处理API响应数据，根据状态码判断请求是否成功，并提供toast提示。

```typescript
import { processResponse } from '@/lib/request';

const result = await processResponse(response, {
  successMessage: "操作成功",      // 成功提示消息
  errorMessage: "请求异常",        // 错误提示消息
  showSuccessToast: false,        // 是否显示成功提示
  showErrorToast: true,           // 是否显示错误提示
  customCodeCheck: (code) => boolean // 自定义状态码检查函数
});
```

### processArrayResponse

处理返回数组的API响应，确保即使API出错也返回空数组而不是null。

```typescript
import { processArrayResponse } from '@/lib/request';

const items = await processArrayResponse(response, options);
// 即使response为null或undefined，items始终是数组(可能为空)
```

### handleApiError

统一处理API异常，并提供友好的错误信息。

```typescript
import { handleApiError } from '@/lib/request';

try {
  // API请求代码
} catch (error) {
  const { message } = handleApiError(
    error,                // 捕获的异常
    "操作失败",           // 默认错误消息
    true                  // 是否显示Toast提示
  );
  // 可以进一步处理错误
}
```

### safeExecute

安全执行异步操作的包装函数，简化try-catch和错误处理。

```typescript
import { safeExecute } from '@/lib/request';

const result = await safeExecute(
  async () => {
    // 异步操作代码
    return data;
  },
  "操作执行失败",  // 默认错误消息
  {
    showSuccessToast: false,        // 是否显示成功提示
    successMessage: "操作成功",      // 成功提示消息
    showErrorToast: true,           // 是否显示错误提示
    onSuccess: (result) => void,    // 成功回调
    onError: (error) => void,       // 错误回调
    retryCount: 0                   // 失败重试次数
  }
);
```

## 使用示例

### 基本使用

```typescript
import { processResponse, handleApiError } from '@/lib/request';
import { request } from '@/lib/request';
import { ApiResponse } from '@/types/api';

export const getUserById = async (id: string) => {
  try {
    const response = await request.get<ApiResponse<User>>(`/api/users/${id}`);
    
    return processResponse(response, {
      errorMessage: "获取用户数据失败"
    });
  } catch (error) {
    handleApiError(error, "获取用户数据失败");
    return null;
  }
};
```

### 高级使用

```typescript
import { safeExecute, processResponse } from '@/lib/request';
import { request } from '@/lib/request';
import { ApiResponse } from '@/types/api';

export const createUser = async (userData) => {
  return safeExecute(
    async () => {
      const response = await request.post<ApiResponse<User>>('/api/users', userData);
      
      const user = processResponse(response);
      
      if (user) {
        // 执行后续操作
        await updateUserPermissions(user.id);
      }
      
      return user;
    },
    "创建用户失败",
    {
      successMessage: "用户创建成功",
      showSuccessToast: true,
      retryCount: 1
    }
  );
};
```

### 自定义状态码检查

```typescript
import { processArrayResponse } from '@/lib/request';

const data = await processArrayResponse(response, {
  customCodeCheck: (code) => {
    // 支持多种成功状态码格式
    return [200, "200", 0, "0", "OK", "SUCCESS"].includes(code);
  }
});
``` 