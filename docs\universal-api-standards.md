# 通用API接口规范

本文档规范了API请求的通用实现方式、最佳实践和错误处理，适用于前后端开发的所有场景。

## 🎯 核心原则

### 1. 一致性原则
- **统一的错误处理机制**
- **标准化的响应格式**
- **一致的命名约定**
- **统一的状态码使用**

### 2. 类型安全原则
- **完整的TypeScript类型定义**
- **请求参数类型验证**
- **响应数据类型保证**
- **错误类型明确定义**

### 3. 用户体验原则
- **合理的加载状态反馈**
- **友好的错误提示**
- **适当的重试机制**
- **优雅的降级处理**

## 📋 标准响应格式

### 成功响应格式

```typescript
interface ApiResponse<T = any> {
  code: number;        // 状态码，200表示成功
  message: string;     // 响应消息
  data: T;            // 响应数据
  success: boolean;    // 是否成功
  timestamp?: number;  // 时间戳（可选）
}

// 示例
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "用户名"
  },
  "success": true,
  "timestamp": 1640995200000
}
```

### 错误响应格式

```typescript
interface ApiError {
  code: number;        // 错误码
  message: string;     // 错误消息
  data?: any;         // 错误详情（可选）
  success: false;      // 固定为false
  timestamp?: number;  // 时间戳（可选）
}

// 示例
{
  "code": 400,
  "message": "参数错误",
  "data": {
    "field": "email",
    "reason": "邮箱格式不正确"
  },
  "success": false,
  "timestamp": 1640995200000
}
```

## 🔢 标准状态码

### 成功状态码
- **200**: 请求成功
- **201**: 创建成功
- **204**: 删除成功（无内容返回）

### 客户端错误状态码
- **400**: 请求参数错误
- **401**: 未授权，需要登录
- **403**: 禁止访问，权限不足
- **404**: 资源不存在
- **409**: 资源冲突
- **422**: 请求参数验证失败

### 服务器错误状态码
- **500**: 服务器内部错误
- **502**: 网关错误
- **503**: 服务不可用
- **504**: 网关超时

## 🛠️ 请求方法规范

### GET请求
用于获取数据，不应有副作用

```typescript
// 获取单个资源
GET /api/users/123

// 获取资源列表
GET /api/users?page=1&size=10&role=admin

// 搜索资源
GET /api/users/search?keyword=张三
```

### POST请求
用于创建新资源

```typescript
// 创建用户
POST /api/users
Content-Type: application/json

{
  "name": "张三",
  "email": "<EMAIL>",
  "role": "user"
}
```

### PUT请求
用于完整更新资源

```typescript
// 更新用户（完整替换）
PUT /api/users/123
Content-Type: application/json

{
  "name": "李四",
  "email": "<EMAIL>",
  "role": "admin"
}
```

### PATCH请求
用于部分更新资源

```typescript
// 部分更新用户
PATCH /api/users/123
Content-Type: application/json

{
  "role": "admin"
}
```

### DELETE请求
用于删除资源

```typescript
// 删除用户
DELETE /api/users/123

// 批量删除
DELETE /api/users
Content-Type: application/json

{
  "ids": [123, 124, 125]
}
```

## 🔐 认证与授权

### 认证方式

1. **Bearer Token认证**
```typescript
Authorization: Bearer <access_token>
```

2. **API Key认证**
```typescript
X-API-Key: <api_key>
```

3. **基础认证**
```typescript
Authorization: Basic <base64(username:password)>
```

### 权限控制

```typescript
// 权限不足响应
{
  "code": 403,
  "message": "权限不足，无法访问该资源",
  "success": false
}

// Token过期响应
{
  "code": 401,
  "message": "Token已过期，请重新登录",
  "success": false
}
```

## 📝 请求参数规范

### 查询参数
```typescript
interface PaginationParams {
  page?: number;      // 页码，从1开始
  size?: number;      // 每页数量，默认10
  sort?: string;      // 排序字段
  order?: 'asc' | 'desc'; // 排序方向
}

interface SearchParams {
  keyword?: string;   // 搜索关键词
  filters?: Record<string, any>; // 过滤条件
}
```

### 请求体参数
```typescript
// 创建用户参数
interface CreateUserParams {
  name: string;
  email: string;
  role: 'admin' | 'user';
  avatar?: string;
}

// 更新用户参数
interface UpdateUserParams {
  name?: string;
  email?: string;
  role?: 'admin' | 'user';
  avatar?: string;
}
```

## ⚠️ 错误处理规范

### 错误分类

1. **网络错误**
```typescript
{
  "code": 0,
  "message": "网络连接失败，请检查网络设置",
  "success": false
}
```

2. **超时错误**
```typescript
{
  "code": 408,
  "message": "请求超时，请稍后重试",
  "success": false
}
```

3. **服务器错误**
```typescript
{
  "code": 500,
  "message": "服务器内部错误，请联系管理员",
  "success": false
}
```

### 错误处理最佳实践

1. **提供有意义的错误消息**
2. **包含错误代码便于调试**
3. **提供解决建议**
4. **记录错误日志**
5. **实现重试机制**

## 📋 API设计检查清单

### 设计阶段
- [ ] API路径遵循RESTful规范
- [ ] 使用合适的HTTP方法
- [ ] 定义完整的请求参数类型
- [ ] 定义标准的响应格式
- [ ] 考虑分页和排序需求

### 实现阶段
- [ ] 实现统一的错误处理
- [ ] 添加请求参数验证
- [ ] 实现认证和授权
- [ ] 添加请求日志记录
- [ ] 实现幂等性（如需要）

### 测试阶段
- [ ] 测试正常流程
- [ ] 测试异常情况
- [ ] 测试边界条件
- [ ] 测试并发访问
- [ ] 测试性能表现

## 🚀 最佳实践

1. **版本控制**：使用版本号管理API变更
2. **文档维护**：保持API文档与实现同步
3. **向后兼容**：新版本保持向后兼容性
4. **性能优化**：合理使用缓存和分页
5. **安全防护**：实施必要的安全措施

遵循这些规范，可以确保API的一致性、可维护性和用户体验。
