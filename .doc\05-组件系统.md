# 组件系统

我们的组件系统基于Shadcn UI，采用了组合式设计，同时融合了Notion的简洁美学风格。本文档详细说明了组件的分类、使用方法和自定义方式。

## 组件架构

我们的组件系统采用分层架构，保证了灵活性和一致性：

1. **基础层** - 最底层的UI原语，如按钮、输入框等基础组件
2. **复合层** - 由多个基础组件组合而成的功能性组件，如表单、卡片等
3. **模板层** - 特定场景的完整解决方案，如登录页面、设置界面等

### 组件目录结构

```
components/
├── ui/          # shadcn/ui基础组件
├── custom/      # 自定义组件
├── layout/      # 布局相关组件
├── navigation/  # 导航相关组件
├── pages/       # 页面特定组件
└── common/      # 通用组件
```

## 基础组件

基础组件是整个UI系统的基石，它们具有高度的可复用性和一致的设计语言。

### 按钮 (Button)

按钮组件提供多种变体以适应不同场景。

#### 变体类型

- **默认 (default)** - 主要操作按钮
- **轮廓 (outline)** - 次要操作按钮，有边框无背景
- **次要 (secondary)** - 次要操作按钮，有浅色背景
- **幽灵 (ghost)** - 最低强调度按钮，无背景无边框
- **链接 (link)** - 以链接形式呈现的按钮
- **危险 (destructive)** - 表示危险或删除操作的按钮

#### 尺寸变体

- **默认** - 标准尺寸
- **sm** - 小尺寸
- **lg** - 大尺寸
- **icon** - 图标按钮（正方形）

#### 使用示例

```jsx
import { Button } from "@/components/ui/button"

export function ButtonDemo() {
  return (
    <div className="flex flex-wrap gap-4">
      <Button>默认按钮</Button>
      <Button variant="outline">轮廓按钮</Button>
      <Button variant="secondary">次要按钮</Button>
      <Button variant="ghost">幽灵按钮</Button>
      <Button variant="link">链接按钮</Button>
      <Button variant="destructive">危险按钮</Button>
      
      {/* 尺寸变体 */}
      <Button size="sm">小按钮</Button>
      <Button size="lg">大按钮</Button>
      <Button size="icon"><IconComponent /></Button>
      
      {/* 禁用状态 */}
      <Button disabled>禁用按钮</Button>
      
      {/* 加载状态 */}
      <Button>
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        加载中
      </Button>
    </div>
  )
}
```

### 输入框 (Input)

输入框组件用于收集用户输入的文本信息。

#### 变体类型

- **默认** - 标准输入框
- **带文件** - 文件上传输入框
- **带图标** - 内置图标的输入框

#### 使用示例

```jsx
import { Input } from "@/components/ui/input"
import { Search } from "lucide-react"

export function InputDemo() {
  return (
    <div className="flex flex-col gap-4 w-full max-w-sm">
      {/* 基本输入框 */}
      <Input placeholder="基本输入框" />
      
      {/* 禁用状态 */}
      <Input disabled placeholder="禁用输入框" />
      
      {/* 带图标输入框 */}
      <div className="relative">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input className="pl-8" placeholder="搜索..." />
      </div>
      
      {/* 文件输入框 */}
      <Input type="file" />
      
      {/* 错误状态 */}
      <Input 
        placeholder="错误状态" 
        className="border-destructive focus-visible:ring-destructive" 
      />
    </div>
  )
}
```

### 下拉选择 (Select)

下拉选择组件用于从预定义选项中进行选择。

```jsx
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export function SelectDemo() {
  return (
    <Select>
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder="选择一个选项" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="option1">选项一</SelectItem>
        <SelectItem value="option2">选项二</SelectItem>
        <SelectItem value="option3">选项三</SelectItem>
      </SelectContent>
    </Select>
  )
}
```

### 复选框 (Checkbox)

复选框组件用于多选场景。

```jsx
import { Checkbox } from "@/components/ui/checkbox"

export function CheckboxDemo() {
  return (
    <div className="flex items-center space-x-2">
      <Checkbox id="terms" />
      <label
        htmlFor="terms"
        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
      >
        接受条款和条件
      </label>
    </div>
  )
}
```

## 复合组件

复合组件由多个基础组件组合而成，提供更复杂的功能和交互。

### 卡片 (Card)

卡片是内容容器的标准方式，用于组织相关信息。

```jsx
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"

export function CardDemo() {
  return (
    <Card className="w-[350px]">
      <CardHeader>
        <CardTitle>卡片标题</CardTitle>
        <CardDescription>卡片的简短描述信息</CardDescription>
      </CardHeader>
      <CardContent>
        <p>卡片的主要内容区域，可以包含各种UI元素。</p>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline">取消</Button>
        <Button>确认</Button>
      </CardFooter>
    </Card>
  )
}
```

### 表单 (Form)

表单组件提供了完整的表单解决方案，包括验证、错误处理等功能。

```jsx
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"

import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"

// 定义表单验证模式
const formSchema = z.object({
  username: z.string().min(2, {
    message: "用户名至少需要2个字符。",
  }),
})

export function FormDemo() {
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: "",
    },
  })

  function onSubmit(values) {
    console.log(values)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormLabel>用户名</FormLabel>
              <FormControl>
                <Input placeholder="输入用户名" {...field} />
              </FormControl>
              <FormDescription>
                这将是您的公开显示名称。
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit">提交</Button>
      </form>
    </Form>
  )
}
```

### 对话框 (Dialog)

对话框组件用于显示需要用户注意或交互的重要信息。

```jsx
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export function DialogDemo() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline">编辑个人资料</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>编辑个人资料</DialogTitle>
          <DialogDescription>
            在这里修改您的个人资料信息。完成后点击保存。
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              姓名
            </Label>
            <Input
              id="name"
              defaultValue="王小明"
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="username" className="text-right">
              用户名
            </Label>
            <Input
              id="username"
              defaultValue="xiaoming"
              className="col-span-3"
            />
          </div>
        </div>
        <DialogFooter>
          <Button type="submit">保存更改</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
```

## 导航组件

导航组件帮助用户在应用中进行导航和定位。

### 导航菜单 (NavigationMenu)

导航菜单提供了一个水平导航解决方案，适用于主导航。

```jsx
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu"
import Link from "next/link"

export function NavigationMenuDemo() {
  return (
    <NavigationMenu>
      <NavigationMenuList>
        <NavigationMenuItem>
          <NavigationMenuTrigger>开始使用</NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className="grid gap-3 p-4 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
              <li className="row-span-3">
                <NavigationMenuLink asChild>
                  <a
                    className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-muted/50 to-muted p-6 no-underline outline-none focus:shadow-md"
                    href="/"
                  >
                    <div className="mb-2 mt-4 text-lg font-medium">
                      产品名称
                    </div>
                    <p className="text-sm leading-tight text-muted-foreground">
                      产品简短描述信息
                    </p>
                  </a>
                </NavigationMenuLink>
              </li>
              <li>
                <Link href="/docs" legacyBehavior passHref>
                  <NavigationMenuLink className={navigationMenuTriggerStyle()}>
                    文档
                  </NavigationMenuLink>
                </Link>
              </li>
              <li>
                <Link href="/docs/components" legacyBehavior passHref>
                  <NavigationMenuLink className={navigationMenuTriggerStyle()}>
                    组件
                  </NavigationMenuLink>
                </Link>
              </li>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <Link href="/docs" legacyBehavior passHref>
            <NavigationMenuLink className={navigationMenuTriggerStyle()}>
              文档
            </NavigationMenuLink>
          </Link>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  )
}
```

### 标签页 (Tabs)

标签页组件用于在同一空间内组织和切换不同内容。

```jsx
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export function TabsDemo() {
  return (
    <Tabs defaultValue="account" className="w-[400px]">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="account">账户</TabsTrigger>
        <TabsTrigger value="password">密码</TabsTrigger>
      </TabsList>
      <TabsContent value="account">
        <div className="p-4 rounded-md border mt-4">
          账户设置内容
        </div>
      </TabsContent>
      <TabsContent value="password">
        <div className="p-4 rounded-md border mt-4">
          密码设置内容
        </div>
      </TabsContent>
    </Tabs>
  )
}
```

## 数据展示组件

数据展示组件用于以结构化方式展示各类数据。

### 表格 (Table)

表格组件用于展示结构化数据。

```jsx
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

export function TableDemo() {
  return (
    <Table>
      <TableCaption>最近的账单记录</TableCaption>
      <TableHeader>
        <TableRow>
          <TableHead className="w-[100px]">账单号</TableHead>
          <TableHead>状态</TableHead>
          <TableHead>方法</TableHead>
          <TableHead className="text-right">金额</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow>
          <TableCell className="font-medium">INV001</TableCell>
          <TableCell>已支付</TableCell>
          <TableCell>信用卡</TableCell>
          <TableCell className="text-right">¥250.00</TableCell>
        </TableRow>
        <TableRow>
          <TableCell className="font-medium">INV002</TableCell>
          <TableCell>待付款</TableCell>
          <TableCell>支付宝</TableCell>
          <TableCell className="text-right">¥150.00</TableCell>
        </TableRow>
      </TableBody>
    </Table>
  )
}
```

### 日历 (Calendar)

日历组件用于日期选择和展示。

```jsx
import { useState } from "react"
import { Calendar } from "@/components/ui/calendar"

export function CalendarDemo() {
  const [date, setDate] = useState(new Date())

  return (
    <Calendar
      mode="single"
      selected={date}
      onSelect={setDate}
      className="rounded-md border"
    />
  )
}
```

## 自定义组件

除了Shadcn UI提供的基础组件外，我们还开发了一系列符合项目需求的自定义组件。

### 页面标题 (PageTitle)

用于页面标题区域的一致展示。

```jsx
import { PageTitle } from "@/components/custom/page-title"

export function PageTitleDemo() {
  return (
    <PageTitle 
      title="项目概览" 
      description="查看所有项目的关键指标和状态"
      actions={
        <Button>新建项目</Button>
      }
    />
  )
}
```

### 统计卡片 (StatCard)

用于展示关键统计数据的卡片组件。

```jsx
import { StatCard } from "@/components/custom/stat-card"
import { TrendingUp, Users, FileText } from "lucide-react"

export function StatCardDemo() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <StatCard 
        title="总用户" 
        value="2,543" 
        change="+12.5%" 
        trend="up"
        icon={<Users className="h-5 w-5" />}
      />
      <StatCard 
        title="总收入" 
        value="¥45,234" 
        change="+5.3%" 
        trend="up"
        icon={<TrendingUp className="h-5 w-5" />}
      />
      <StatCard 
        title="总文档" 
        value="342" 
        change="-2.7%" 
        trend="down"
        icon={<FileText className="h-5 w-5" />}
      />
    </div>
  )
}
```

## Notion风格组件

我们设计了一系列Notion风格的组件，提供类似Notion的用户体验。

### 块编辑器 (BlockEditor)

模仿Notion的块编辑器，支持多种内容块类型。

```jsx
import { BlockEditor } from "@/components/custom/block-editor"

export function BlockEditorDemo() {
  const initialBlocks = [
    { id: "1", type: "heading", content: "项目文档" },
    { id: "2", type: "paragraph", content: "这是一个简单的项目文档示例。" },
  ]

  return (
    <BlockEditor 
      initialBlocks={initialBlocks}
      onChange={(blocks) => console.log(blocks)}
    />
  )
}
```

### 数据库视图 (DatabaseView)

类似Notion的数据库视图，支持表格、看板、日历等多种视图模式。

```jsx
import { DatabaseView } from "@/components/custom/database-view"

export function DatabaseViewDemo() {
  const columns = [
    { id: "name", title: "名称", type: "text" },
    { id: "status", title: "状态", type: "select" },
    { id: "dueDate", title: "截止日期", type: "date" },
  ]
  
  const items = [
    { id: "1", name: "项目A", status: "进行中", dueDate: "2023-12-31" },
    { id: "2", name: "项目B", status: "计划中", dueDate: "2024-01-15" },
  ]

  return (
    <DatabaseView 
      columns={columns}
      items={items}
      defaultView="table"
      onItemChange={(item) => console.log(item)}
    />
  )
}
```

## 组件使用规范

### 组件命名约定

- 组件文件使用kebab-case命名，如`page-header.tsx`
- 组件名使用PascalCase命名，如`PageHeader`
- 组件属性使用camelCase命名，如`onValueChange`

### 组件文档规范

每个自定义组件都应提供完整的文档，包括：

1. **组件描述** - 简要说明组件的用途和适用场景
2. **属性定义** - 详细列出所有属性、类型和默认值
3. **使用示例** - 提供基本使用示例代码
4. **变体说明** - 如果有不同变体，说明每种变体的用途
5. **注意事项** - 使用时需要注意的问题或限制

### 组件最佳实践

1. **组合而非继承** - 优先使用组合模式，而非继承复杂组件
2. **保持简单** - 每个组件应专注于单一职责
3. **默认值合理** - 提供合理的默认值，减少必需属性
4. **响应式设计** - 组件应自适应不同屏幕尺寸
5. **无障碍支持** - 组件应支持键盘导航和屏幕阅读器

## 组件主题化

### 全局主题变量

组件主题基于CSS变量系统，可以通过修改根变量统一调整：

```css
:root {
  --background: oklch(0.985 0 0);
  --foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  /* 其他主题变量... */
}
```

### 组件样式覆盖

可以通过CSS或className属性覆盖组件样式：

```jsx
// 使用className覆盖样式
<Button className="bg-custom text-custom-foreground">
  自定义按钮
</Button>

// 使用全局CSS覆盖
/*
.button-primary {
  background-color: var(--custom-color);
  color: white;
}
*/
```

### 深色模式支持

所有组件都支持深色模式，通过CSS变量动态切换：

```css
.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  /* 其他深色模式变量... */
}
```

## 组件性能优化

1. **组件懒加载** - 使用React.lazy和Suspense延迟加载非关键组件
2. **按需导入** - 只导入所需的组件，避免不必要的代码加载
3. **虚拟化列表** - 大数据列表使用虚拟化技术（如react-window）
4. **React.memo** - 为纯展示组件使用React.memo避免不必要的重渲染
5. **合理的状态管理** - 避免状态提升过高导致的大范围重渲染 