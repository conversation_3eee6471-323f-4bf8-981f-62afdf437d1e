"use client"

import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { cn } from "@/lib/utils"

interface BackButtonProps {
  href: string
  className?: string
  children?: React.ReactNode
  showText?: boolean
}

export function BackButton({ href, className, children, showText = false }: BackButtonProps) {
  return (
    <Link 
      href={href}
      className={cn(
        "inline-flex items-center justify-center h-9 px-3 rounded-md transition-all cursor-pointer hover:text-primary hover:bg-muted/70 hover:shadow-sm focus:ring-2 focus:ring-primary/40",
        className
      )}
    >
      <ArrowLeft className="h-5 w-5" />
      {showText && children}
    </Link>
  )
} 