# 布局网格系统

布局网格是整个界面的骨架，为组件提供一致的对齐和间距规则。我们的网格系统基于现代响应式设计原则，同时融合了Shadcn UI与Notion的简洁美学。

## 基础网格

我们采用12列网格系统，这是现代UI设计中最灵活的网格方案，可以轻松划分为2、3、4、6等均等部分。

### 网格属性

```css
:root {
  /* 网格属性 */
  --grid-columns: 12;
  --grid-max-width: 1440px;
  --grid-container-padding: 1rem; /* 移动端边距 */
  --grid-container-padding-lg: 2rem; /* 桌面端边距 */
}

@media (min-width: 1024px) {
  :root {
    --grid-container-padding: var(--grid-container-padding-lg);
  }
}
```

### 基本布局容器

```jsx
// 标准容器
<div className="container mx-auto px-4 lg:px-8">
  {/* 内容区域 */}
</div>

// 全宽容器
<div className="w-full">
  {/* 全宽内容区域 */}
</div>

// 有最大宽度的容器
<div className="mx-auto w-full max-w-screen-xl px-4 lg:px-8">
  {/* 内容区域 */}
</div>
```

## 间距系统

我们的间距系统基于8px作为基本单位，创建和谐的视觉节奏。这种8点栅格系统(8pt Grid System)在现代UI设计中被广泛应用。

### 间距变量

```css
:root {
  /* 间距变量 - 8的倍数 */
  --space-1: 0.25rem; /* 4px */
  --space-2: 0.5rem; /* 8px */
  --space-3: 0.75rem; /* 12px */
  --space-4: 1rem; /* 16px */
  --space-5: 1.25rem; /* 20px */
  --space-6: 1.5rem; /* 24px */
  --space-8: 2rem; /* 32px */
  --space-10: 2.5rem; /* 40px */
  --space-12: 3rem; /* 48px */
  --space-16: 4rem; /* 64px */
  --space-20: 5rem; /* 80px */
  --space-24: 6rem; /* 96px */
}
```

### 间距应用规则

间距使用应当遵循以下原则：

1. **垂直间距**：相关内容组之间使用较小间距（`space-4`到`space-8`），不同内容块之间使用较大间距（`space-12`到`space-16`）
2. **水平间距**：相关元素之间使用较小间距（`space-2`到`space-4`），不同功能区域之间使用较大间距（`space-8`到`space-12`）
3. **层级间距**：子元素内边距通常比父元素内边距小一级
4. **响应式调整**：在移动设备上适当减小间距，在大屏幕上适当增加间距

```jsx
// 间距应用示例
<section className="py-12 md:py-16 lg:py-20">
  <div className="container">
    <h2 className="mb-8">标题</h2>
    <div className="grid gap-6 md:gap-8">
      {/* 内容卡片 */}
    </div>
  </div>
</section>
```

## 响应式断点

我们定义了5个核心断点，涵盖从移动设备到大型显示器的各种尺寸。

| 断点名称 | 像素范围 | CSS 媒体查询 | 主要目标设备 |
|---------|---------|------------|------------|
| xs | <640px | `@media (max-width: 639px)` | 手机（竖屏） |
| sm | 640px-767px | `@media (min-width: 640px)` | 手机（横屏）/小平板 |
| md | 768px-1023px | `@media (min-width: 768px)` | 平板设备 |
| lg | 1024px-1279px | `@media (min-width: 1024px)` | 笔记本电脑 |
| xl | 1280px-1535px | `@media (min-width: 1280px)` | 桌面显示器 |
| 2xl | ≥1536px | `@media (min-width: 1536px)` | 大型显示器 |

### Tailwind CSS断点

```js
// tailwind.config.js
module.exports = {
  theme: {
    screens: {
      'xs': {'max': '639px'},
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
  }
}
```

### 响应式布局示例

```jsx
// 响应式网格布局
<div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
  {/* 网格项 */}
</div>

// 响应式Flexbox布局
<div className="flex flex-col md:flex-row md:space-x-6">
  <div className="w-full md:w-1/3">
    {/* 侧边栏 */}
  </div>
  <div className="w-full md:w-2/3 mt-6 md:mt-0">
    {/* 主内容 */}
  </div>
</div>
```

## 常用布局模式

### 基本页面布局

```jsx
// 标准页面布局
<div className="min-h-screen flex flex-col">
  <header className="h-16 border-b">
    {/* 头部内容 */}
  </header>
  
  <main className="flex-1 py-8">
    <div className="container">
      {/* 页面主体内容 */}
    </div>
  </main>
  
  <footer className="bg-muted py-8">
    <div className="container">
      {/* 页脚内容 */}
    </div>
  </footer>
</div>
```

### 侧边栏与内容布局

```jsx
// 侧边栏布局
<div className="min-h-screen flex flex-col md:flex-row">
  {/* 侧边栏 */}
  <aside className="w-full md:w-64 md:fixed md:h-screen bg-card border-r">
    <div className="p-4">
      {/* 侧边栏内容 */}
    </div>
  </aside>
  
  {/* 主内容区 */}
  <main className="flex-1 md:ml-64 p-6">
    {/* 主内容 */}
  </main>
</div>
```

### 卡片网格布局

```jsx
// 卡片网格
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
  {items.map(item => (
    <div key={item.id} className="bg-card rounded-lg border p-6">
      {/* 卡片内容 */}
    </div>
  ))}
</div>
```

### 分栏布局

```jsx
// 两栏布局
<div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
  <div>
    {/* 左栏内容 */}
  </div>
  <div>
    {/* 右栏内容 */}
  </div>
</div>

// 三栏布局
<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
  <div>
    {/* 栏目1 */}
  </div>
  <div>
    {/* 栏目2 */}
  </div>
  <div>
    {/* 栏目3 */}
  </div>
</div>
```

### 居中内容布局

```jsx
// 居中内容（如登录表单）
<div className="min-h-screen flex items-center justify-center p-4">
  <div className="w-full max-w-md bg-card rounded-lg border p-8">
    {/* 居中内容 */}
  </div>
</div>
```

## Notion风格页面布局

Notion采用简洁、留白充足的页面布局，我们可以通过以下模式实现类似效果：

```jsx
// Notion风格页面
<div className="max-w-3xl mx-auto px-4 py-10">
  {/* 页面标题 */}
  <h1 className="text-3xl font-bold mb-8">页面标题</h1>
  
  {/* 页面内容块 */}
  <div className="space-y-6">
    <div className="p-6 rounded-lg border">
      {/* 内容块1 */}
    </div>
    
    <div className="p-6 rounded-lg border">
      {/* 内容块2 */}
    </div>
    
    {/* 更多内容块... */}
  </div>
</div>
```

## 自适应容器查询

除了基于视口的响应式设计，我们还使用现代CSS容器查询来实现基于父容器大小的响应式布局：

```css
/* 容器查询示例 */
.card-container {
  container-type: inline-size;
}

@container (min-width: 400px) {
  .card-content {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 1rem;
  }
}
```

```jsx
// React组件中的应用
<div className="card-container">
  <div className="card-content">
    <div className="card-image">
      {/* 图片内容 */}
    </div>
    <div className="card-body">
      {/* 卡片正文 */}
    </div>
  </div>
</div>
```

## 特殊布局组件

### 分割面板

```jsx
// 分割面板布局
<div className="grid grid-cols-1 md:grid-cols-2 h-screen">
  <div className="bg-card p-8 overflow-auto">
    {/* 左侧面板内容 */}
  </div>
  <div className="bg-background p-8 overflow-auto">
    {/* 右侧面板内容 */}
  </div>
</div>
```

### 固定高度内容

```jsx
// 固定高度内容区域
<div className="h-[600px] overflow-auto rounded-lg border">
  {/* 滚动内容 */}
</div>
```

### 粘性头部

```jsx
// 粘性头部布局
<div className="min-h-screen">
  <header className="sticky top-0 z-10 bg-background border-b">
    {/* 固定在顶部的导航 */}
  </header>
  
  <main className="py-8">
    {/* 主内容滚动 */}
  </main>
</div>
```

## 布局最佳实践

### 推荐做法

1. **移动优先设计**：始终从移动视图开始设计，再逐步扩展到大屏幕
2. **使用相对单位**：优先使用rem、em、%等相对单位，而非固定像素值
3. **流式布局**：使用流式布局（flex、grid）代替绝对定位
4. **布局一致性**：在相似页面中保持一致的布局模式和间距规则

### 避免事项

1. **避免固定高度**：除非必要，避免给元素设置固定高度，防止内容溢出
2. **避免过度嵌套**：控制HTML结构的嵌套层级，通常不超过4-5层
3. **避免重叠断点**：断点应该明确不重叠，避免在边界处出现问题
4. **避免特殊断点**：尽量使用预定义的断点，保持一致性

## 实际案例：仪表盘布局

```jsx
// 仪表盘布局示例
<div className="min-h-screen flex flex-col">
  {/* 顶部导航 */}
  <header className="h-16 border-b px-4 flex items-center justify-between">
    <div className="flex items-center">
      <Logo className="h-8 w-auto" />
    </div>
    <nav className="hidden md:flex space-x-4">
      {/* 导航项 */}
    </nav>
    <div className="flex items-center">
      {/* 用户菜单 */}
    </div>
  </header>

  <div className="flex-1 flex flex-col md:flex-row">
    {/* 侧边栏 */}
    <aside className="w-full md:w-64 border-r md:h-[calc(100vh-4rem)]">
      <nav className="p-4">
        {/* 侧边栏导航 */}
      </nav>
    </aside>

    {/* 主内容区 */}
    <main className="flex-1 p-6 overflow-auto">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-2xl font-semibold">仪表盘</h1>
      </div>

      {/* 卡片网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {/* 统计卡片 */}
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 图表内容 */}
      </div>
    </main>
  </div>
</div>
``` 