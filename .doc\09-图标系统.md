# 图标系统

图标是用户界面中的重要视觉元素，能够高效传达信息和功能。本文档规范了项目中图标的使用原则、样式指南和实现方法。

## 图标设计原则

### 1. 一致性

- 使用统一的设计语言，保持风格一致
- 相似功能使用相似图标，减少学习成本
- 所有图标遵循统一的网格系统和规则

### 2. 简洁性

- 去除不必要的细节，保留核心含义
- 优先使用单色线性图标，避免复杂的多色图标
- 确保在小尺寸下仍清晰可辨

### 3. 语义清晰

- 图标应直观表达其功能或含义
- 避免使用晦涩或文化差异大的隐喻
- 必要时搭配文字标签增强理解

## 图标系统基础

### 图标库

我们使用[Lucide](https://lucide.dev/)作为主要图标库，它提供了丰富的线性图标集，与Shadcn UI和Notion风格高度契合。

```jsx
import { FileText, Settings, User, Home } from "lucide-react"

function IconDemo() {
  return (
    <div className="flex gap-4">
      <Home className="h-6 w-6" />
      <FileText className="h-6 w-6" />
      <Settings className="h-6 w-6" />
      <User className="h-6 w-6" />
    </div>
  )
}
```

### 图标尺寸系统

我们定义了标准的图标尺寸，以保持界面的一致性：

| 尺寸名称 | 像素值 | 用途 |
|---------|-------|-----|
| xs | 12×12px | 极小图标，用于紧凑型UI元素、标签 |
| sm | 16×16px | 小图标，用于次要操作、紧凑型按钮 |
| md | 20×20px | 中等图标，用于标准按钮、表单元素 |
| lg | 24×24px | 大图标，用于主要操作、导航项 |
| xl | 32×32px | 特大图标，用于强调显示、特殊功能 |

```jsx
// 图标尺寸示例
<div className="flex flex-col gap-4">
  <div className="flex items-center gap-2">
    <User className="h-3 w-3" /> <span className="text-xs">超小图标 (12px)</span>
  </div>
  <div className="flex items-center gap-2">
    <User className="h-4 w-4" /> <span className="text-sm">小图标 (16px)</span>
  </div>
  <div className="flex items-center gap-2">
    <User className="h-5 w-5" /> <span className="text-base">中等图标 (20px)</span>
  </div>
  <div className="flex items-center gap-2">
    <User className="h-6 w-6" /> <span className="text-lg">大图标 (24px)</span>
  </div>
  <div className="flex items-center gap-2">
    <User className="h-8 w-8" /> <span className="text-xl">特大图标 (32px)</span>
  </div>
</div>
```

### 图标颜色系统

图标颜色应遵循项目的色彩系统，通常与文本颜色保持一致：

```jsx
// 图标颜色示例
<div className="space-y-2">
  <div className="flex items-center gap-2">
    <Info className="h-5 w-5 text-foreground" />
    <span>主要颜色 (foreground)</span>
  </div>
  <div className="flex items-center gap-2">
    <Info className="h-5 w-5 text-muted-foreground" />
    <span className="text-muted-foreground">次要颜色 (muted-foreground)</span>
  </div>
  <div className="flex items-center gap-2">
    <Info className="h-5 w-5 text-primary" />
    <span className="text-primary">强调颜色 (primary)</span>
  </div>
  <div className="flex items-center gap-2">
    <AlertCircle className="h-5 w-5 text-destructive" />
    <span className="text-destructive">警告颜色 (destructive)</span>
  </div>
</div>
```

## 图标用法指南

### 按钮中的图标

```jsx
import { Button } from "@/components/ui/button"
import { Plus, Trash, Pencil } from "lucide-react"

// 仅图标按钮
<Button size="icon" variant="ghost">
  <Plus className="h-4 w-4" />
  <span className="sr-only">添加项目</span>
</Button>

// 带文本的图标按钮
<Button>
  <Plus className="mr-2 h-4 w-4" />
  添加项目
</Button>

// 不同变体的图标按钮
<div className="flex gap-2">
  <Button variant="default">
    <Plus className="mr-2 h-4 w-4" /> 添加
  </Button>
  <Button variant="outline">
    <Pencil className="mr-2 h-4 w-4" /> 编辑
  </Button>
  <Button variant="destructive">
    <Trash className="mr-2 h-4 w-4" /> 删除
  </Button>
</div>
```

### 导航中的图标

```jsx
// 侧边栏导航
<nav className="space-y-1">
  <a 
    href="/dashboard" 
    className="flex items-center px-4 py-2 text-sm font-medium rounded-md bg-primary text-primary-foreground"
  >
    <Home className="mr-3 h-5 w-5" />
    仪表盘
  </a>
  <a 
    href="/projects" 
    className="flex items-center px-4 py-2 text-sm font-medium rounded-md text-foreground hover:bg-secondary"
  >
    <FolderOpen className="mr-3 h-5 w-5" />
    项目
  </a>
  <a 
    href="/calendar" 
    className="flex items-center px-4 py-2 text-sm font-medium rounded-md text-foreground hover:bg-secondary"
  >
    <Calendar className="mr-3 h-5 w-5" />
    日历
  </a>
</nav>
```

### 表单元素中的图标

```jsx
// 带图标的输入框
<div className="relative">
  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
  <Input className="pl-8" placeholder="搜索..." />
</div>

// 带图标的选择框
<div className="relative">
  <Select>
    <SelectTrigger className="w-full pl-8">
      <Calendar className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
      <SelectValue placeholder="选择日期" />
    </SelectTrigger>
    <SelectContent>
      {/* 选项内容 */}
    </SelectContent>
  </Select>
</div>
```

### 状态指示器中的图标

```jsx
// 使用图标表示不同状态
<div className="space-y-2">
  <div className="flex items-center">
    <Badge variant="outline" className="gap-1 text-success-600">
      <CheckCircle className="h-3 w-3" /> 完成
    </Badge>
  </div>
  <div className="flex items-center">
    <Badge variant="outline" className="gap-1 text-warning-600">
      <Clock className="h-3 w-3" /> 等待中
    </Badge>
  </div>
  <div className="flex items-center">
    <Badge variant="outline" className="gap-1 text-error-600">
      <XCircle className="h-3 w-3" /> 失败
    </Badge>
  </div>
</div>
```

## 图标对齐与间距

### 水平对齐

图标与文本的水平对齐应遵循以下规则：

1. **同行文本前的图标**：图标右侧与文本间距为其尺寸的一半（如16px图标，间距为8px）
2. **同行文本后的图标**：图标左侧与文本间距为其尺寸的一半
3. **按钮中的图标**：与按钮文本间距为8px

```jsx
// 图标与文本对齐示例
<div className="space-y-4">
  <div className="flex items-center">
    <Mail className="mr-2 h-4 w-4" />
    <span>文本前的图标</span>
  </div>
  <div className="flex items-center">
    <span>文本后的图标</span>
    <ArrowRight className="ml-2 h-4 w-4" />
  </div>
</div>
```

### 垂直对齐

图标与文本的垂直对齐应遵循以下规则：

1. **单行文本**：图标应与文本在中线对齐（使用`items-center`）
2. **多行文本**：图标应与文本第一行顶部对齐（使用`items-start`）

```jsx
// 垂直对齐示例
<div className="space-y-4">
  <div className="flex items-center">
    <Info className="mr-2 h-5 w-5 flex-shrink-0" />
    <span>单行文本与图标居中对齐</span>
  </div>
  <div className="flex items-start">
    <Info className="mr-2 h-5 w-5 flex-shrink-0 mt-0.5" />
    <div>
      <p>多行文本第一段</p>
      <p className="text-muted-foreground">图标与第一行文本顶部对齐</p>
    </div>
  </div>
</div>
```

## 图标组件

为了确保一致性和可维护性，我们创建了标准的图标组件：

### 图标按钮组件

```jsx
// components/custom/icon-button.tsx
import { Button } from "@/components/ui/button"
import { LucideIcon } from "lucide-react"

interface IconButtonProps {
  icon: LucideIcon
  label: string
  onClick?: () => void
  variant?: "default" | "outline" | "secondary" | "ghost" | "destructive"
}

export function IconButton({ 
  icon: Icon, 
  label, 
  onClick, 
  variant = "default" 
}: IconButtonProps) {
  return (
    <Button 
      variant={variant} 
      size="sm" 
      onClick={onClick}
      className="gap-2"
    >
      <Icon className="h-4 w-4" />
      {label}
    </Button>
  )
}

// 使用示例
<IconButton 
  icon={Plus} 
  label="添加项目" 
  onClick={() => console.log("添加")} 
/>
```

### 图标菜单项组件

```jsx
// components/custom/icon-menu-item.tsx
import { LucideIcon } from "lucide-react"

interface IconMenuItemProps {
  icon: LucideIcon
  label: string
  onClick?: () => void
  active?: boolean
}

export function IconMenuItem({ 
  icon: Icon, 
  label, 
  onClick, 
  active = false 
}: IconMenuItemProps) {
  return (
    <button 
      className={`flex items-center w-full px-3 py-2 text-sm rounded-md ${
        active 
          ? "bg-primary text-primary-foreground" 
          : "text-foreground hover:bg-secondary"
      }`}
      onClick={onClick}
    >
      <Icon className="mr-2 h-4 w-4" />
      {label}
    </button>
  )
}

// 使用示例
<div className="space-y-1 w-48">
  <IconMenuItem 
    icon={Home} 
    label="仪表盘" 
    active={true} 
  />
  <IconMenuItem 
    icon={Settings} 
    label="设置" 
  />
</div>
```

## 图标动效

图标可以添加适当的动效，增强交互体验：

### 简单动效

```jsx
// 悬停缩放效果
<Button 
  variant="outline" 
  className="group"
>
  <RefreshCw className="mr-2 h-4 w-4 transition-transform group-hover:rotate-180" />
  刷新
</Button>

// 加载动画
<Button disabled>
  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
  加载中
</Button>
```

### 状态切换动效

```jsx
// 状态切换动画
function ToggleIconButton() {
  const [isActive, setIsActive] = useState(false)
  
  return (
    <Button
      variant="outline"
      onClick={() => setIsActive(!isActive)}
      className="relative"
    >
      <span className="relative">
        <Heart 
          className={`h-4 w-4 transition-opacity ${
            isActive ? "opacity-0" : "opacity-100"
          }`} 
        />
        <HeartFilled 
          className={`absolute inset-0 h-4 w-4 text-red-500 transition-opacity ${
            isActive ? "opacity-100" : "opacity-0"
          }`} 
        />
      </span>
      <span className="ml-2">
        {isActive ? "已收藏" : "收藏"}
      </span>
    </Button>
  )
}
```

## 主题适配

图标应根据当前主题（亮色/暗色）自动调整颜色：

```jsx
// 主题自适应图标
<div className="dark:hidden">
  {/* 亮色模式图标 */}
  <Sun className="h-5 w-5" />
</div>
<div className="hidden dark:block">
  {/* 暗色模式图标 */}
  <Moon className="h-5 w-5" />
</div>

// 主题切换按钮
<Button variant="ghost" size="icon" className="relative">
  <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
  <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
  <span className="sr-only">切换主题</span>
</Button>
```

## 无障碍性

为确保图标对所有用户都可访问，应遵循以下实践：

### 替代文本

为纯图标按钮或链接添加屏幕阅读器可访问的替代文本：

```jsx
<Button size="icon" variant="ghost">
  <Plus className="h-4 w-4" />
  <span className="sr-only">添加项目</span>
</Button>
```

### 装饰性图标

对于装饰性图标，使用`aria-hidden`属性：

```jsx
<div className="flex items-center">
  <CheckCircle className="mr-2 h-4 w-4 text-success-600" aria-hidden="true" />
  <span>任务已完成</span>
</div>
```

### 焦点状态

确保图标按钮有清晰的焦点状态：

```jsx
<Button 
  size="icon" 
  className="focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
>
  <Settings className="h-4 w-4" />
  <span className="sr-only">设置</span>
</Button>
```

## 自定义图标

除了使用Lucide提供的图标外，有时我们需要创建自定义图标：

### 创建自定义图标组件

```jsx
// components/custom/icons/logo-icon.tsx
import { SVGProps } from "react"

export function LogoIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      viewBox="0 0 24 24" 
      fill="none" 
      stroke="currentColor" 
      strokeWidth="2" 
      strokeLinecap="round" 
      strokeLinejoin="round"
      {...props}
    >
      {/* 自定义图标路径 */}
      <circle cx="12" cy="12" r="10" />
      <path d="M8 12h8" />
      <path d="M12 8v8" />
    </svg>
  )
}

// 使用示例
<LogoIcon className="h-6 w-6 text-primary" />
```

### 图标一致性指南

自定义图标应遵循以下规则以保持与Lucide图标的一致性：

1. 使用24×24像素的viewBox
2. 使用2px的线条宽度
3. 使用圆形的线条端点和连接点
4. 使用currentColor继承父元素颜色
5. 保持简单的线性风格

## 实际应用示例

### 功能丰富的标题栏

```jsx
function PageHeader() {
  return (
    <div className="flex items-center justify-between border-b px-6 py-4">
      <div className="flex items-center gap-2">
        <Notebook className="h-5 w-5" />
        <h1 className="text-xl font-semibold">项目笔记</h1>
      </div>
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon">
          <Bell className="h-5 w-5" />
          <span className="sr-only">通知</span>
        </Button>
        <Button variant="ghost" size="icon">
          <Share className="h-5 w-5" />
          <span className="sr-only">分享</span>
        </Button>
        <Button variant="ghost" size="icon">
          <MoreHorizontal className="h-5 w-5" />
          <span className="sr-only">更多选项</span>
        </Button>
      </div>
    </div>
  )
}
```

### 带图标的数据卡片

```jsx
function StatCard({ icon, title, value, trend, change }) {
  const Icon = icon
  const isPositive = trend === "up"
  
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">
              {title}
            </p>
            <p className="text-2xl font-bold mt-1">
              {value}
            </p>
          </div>
          <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
            <Icon className="h-5 w-5 text-primary" />
          </div>
        </div>
        <div className="mt-4 flex items-center">
          {isPositive ? (
            <TrendingUp className="h-4 w-4 text-success-600 mr-1" />
          ) : (
            <TrendingDown className="h-4 w-4 text-destructive mr-1" />
          )}
          <span className={isPositive ? "text-success-600" : "text-destructive"}>
            {change}
          </span>
          <span className="text-sm text-muted-foreground ml-1">
            与上月相比
          </span>
        </div>
      </CardContent>
    </Card>
  )
}

// 使用示例
<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
  <StatCard 
    icon={Users}
    title="总用户"
    value="2,543"
    trend="up"
    change="+12.5%"
  />
  <StatCard 
    icon={DollarSign}
    title="总收入"
    value="¥45,234"
    trend="up"
    change="+5.3%"
  />
  <StatCard 
    icon={FileText}
    title="总文档"
    value="342"
    trend="down"
    change="-2.7%"
  />
</div>
```

## 最佳实践

### 图标使用推荐做法

1. **保持一致性**：在整个应用中使用一致的图标风格和大小
2. **简化选择**：为团队创建一个有限的核心图标集，避免过多选择造成不一致
3. **确保辨识度**：确保图标在各种尺寸和背景下都清晰可辨
4. **文字配合**：对不明显的功能，将图标与文字标签结合使用
5. **响应式调整**：在移动设备上适当增大图标点击区域

### 图标使用避免事项

1. **避免过度使用**：界面中图标过多会造成视觉混乱
2. **避免不一致的风格**：不要混用不同风格的图标库
3. **避免复杂图标**：除非特殊需要，避免使用复杂的多色或插图式图标
4. **避免无意义图标**：不要使用与功能无关或难以理解的图标
5. **避免频繁变动**：一旦用户熟悉了图标含义，避免经常改变图标 