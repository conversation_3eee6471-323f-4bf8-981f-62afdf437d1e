"use client"

import { ThemeProvider } from "@/components/ui/use-theme"
import { Toaster } from "@/components/ui/sonner"
import { SidebarProvider } from "@/components/navigation/sidebar"
import { AuthProvider } from "@/components/custom/auth-provider"
import { NavigationProvider } from "@/components/providers/navigation-provider"
import { GlobalLoading } from "@/components/custom/global-loading"

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <AuthProvider>
        <NavigationProvider>
          <SidebarProvider defaultOpen={true}>
            {children}
            <Toaster />
            <GlobalLoading />
          </SidebarProvider>
        </NavigationProvider>
      </AuthProvider>
    </ThemeProvider>
  )
} 