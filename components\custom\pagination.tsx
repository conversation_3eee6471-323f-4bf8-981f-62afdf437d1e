"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { cn } from "@/lib/utils"

export interface PaginationProps {
  pageIndex: number
  pageCount: number
  pageSize: number
  pageSizeOptions?: number[]
  totalItems: number
  onPageChange: (page: number) => void
  onPageSizeChange: (size: number) => void
  className?: string
}

export function Pagination({
  pageIndex,
  pageCount,
  pageSize,
  pageSizeOptions = [10, 20, 30, 40, 50],
  totalItems,
  onPageChange,
  onPageSizeChange,
  className,
}: PaginationProps) {
  const [inputValue, setInputValue] = React.useState(pageIndex + 1)

  React.useEffect(() => {
    setInputValue(pageIndex + 1)
  }, [pageIndex])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(Number(e.target.value))
  }

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      const page = inputValue ? Number(inputValue) - 1 : 0
      if (page >= 0 && page < pageCount) {
        onPageChange(page)
      } else {
        setInputValue(pageIndex + 1)
      }
    }
  }

  const handleInputBlur = () => {
    const page = inputValue ? Number(inputValue) - 1 : 0
    if (page >= 0 && page < pageCount) {
      onPageChange(page)
    } else {
      setInputValue(pageIndex + 1)
    }
  }

  return (
    <div className={cn("flex flex-col sm:flex-row items-center justify-between py-4", className)}>
      {/* 左侧显示总数 */}
      <div className="text-sm text-muted-foreground order-2 sm:order-1 mt-2 sm:mt-0">
        总计 {totalItems} 条
      </div>

      {/* 中间是页码切换 - 小屏居中，大屏靠右 */}
      <div className="flex items-center justify-center sm:justify-end sm:ml-auto sm:mr-4 order-1 sm:order-2">
        <div className="flex items-center space-x-1">
          {/* 上一页按钮 */}
          <Button
            variant="ghost"
            size="icon"
            className="w-8 h-8 p-0"
            onClick={() => onPageChange(pageIndex - 1)}
            disabled={pageIndex === 0}
          >
            <span className="sr-only">上一页</span>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          {/* 页码按钮 */}
          <div className="flex items-center">
            {Array.from({ length: Math.min(5, pageCount) }).map((_, i) => {
              let currentPageIndex;
              
              if (pageCount <= 5) {
                // 如果总页数小于等于5，直接显示所有页码
                currentPageIndex = i;
              } else if (pageIndex < 3) {
                // 如果当前页靠近开始，显示前5页
                currentPageIndex = i;
              } else if (pageIndex > pageCount - 3) {
                // 如果当前页靠近结束，显示最后5页
                currentPageIndex = pageCount - 5 + i;
              } else {
                // 显示当前页及其前后各2页
                currentPageIndex = pageIndex - 2 + i;
              }
              
              // 确保页码在有效范围内
              currentPageIndex = Math.max(0, Math.min(pageCount - 1, currentPageIndex));
              
              return (
                <Button
                  key={currentPageIndex}
                  variant={pageIndex === currentPageIndex ? "default" : "outline"}
                  size="sm"
                  className="w-8 h-8 p-0 mx-0.5"
                  onClick={() => onPageChange(currentPageIndex)}
                >
                  {currentPageIndex + 1}
                </Button>
              );
            })}
          </div>
          
          {/* 下一页按钮 */}
          <Button
            variant="ghost"
            size="icon"
            className="w-8 h-8 p-0"
            onClick={() => onPageChange(pageIndex + 1)}
            disabled={pageIndex >= pageCount - 1}
          >
            <span className="sr-only">下一页</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* 右侧是展示条数和跳转页码 - 小屏隐藏 */}
      <div className="hidden sm:flex items-center space-x-4 order-3">
        <div className="flex items-center">
          <Select
            value={`${pageSize}`}
            onValueChange={(value) => onPageSizeChange(Number(value))}
          >
            <SelectTrigger className="w-[105px]">
              <SelectValue placeholder={`${pageSize} 条/页`} />
            </SelectTrigger>
            <SelectContent side="top">
              {pageSizeOptions.map((size) => (
                <SelectItem key={size} value={`${size}`}>
                  {size} 条/页
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center">
          <span className="text-sm mr-2">跳至</span>
          <Input
            className="w-14 h-8 text-center"
            type="number"
            min={1}
            max={pageCount}
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleInputKeyDown}
            onBlur={handleInputBlur}
            style={{
              appearance: "textfield",
              MozAppearance: "textfield",
              WebkitAppearance: "textfield"
            }}
          />
          <span className="text-sm ml-2">页</span>
        </div>
      </div>
    </div>
  )
} 