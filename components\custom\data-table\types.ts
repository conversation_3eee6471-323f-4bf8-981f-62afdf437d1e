import type { LucideIcon } from "lucide-react"
import type { ComponentType, ReactElement } from "react"

export interface FilterOption {
    label: string
    value: string
    icon?: LucideIcon | ComponentType<{ className?: string }>
    color?: string
}

export interface TableFilter {
    key: string
    title: string
    type?: "select" | "multi-select" | "date-range" | "search"
    options?: FilterOption[]
    placeholder?: string
}

export interface TableAction {
    label: string
    value: string
    icon?: ComponentType<{ className?: string }>
    variant?: "default" | "destructive" | "outline" | "secondary" | "ghost"
    onClick?: (row: any) => void
}

export interface TableConfig {
    showSelection?: boolean
    showPagination?: boolean
    showSearch?: boolean
    showFilters?: boolean
    showColumnVisibility?: boolean
    showRowActions?: boolean
    pageSize?: number
    pageSizeOptions?: number[]
    searchPlaceholder?: string
    emptyMessage?: string
    loadingMessage?: string
}

export interface DataTableProps<TData, TValue> {
    columns: any[]
    data: TData[]
    config?: TableConfig
    searchKey?: string
    filters?: TableFilter[]
    primaryActions?: TableAction[]
    secondaryActions?: TableAction[]
    onRowAction?: (row: TData, action: string) => void
    loading?: boolean
    className?: string
    onRefresh?: () => void
}
