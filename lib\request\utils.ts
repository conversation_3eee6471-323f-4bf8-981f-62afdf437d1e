import { toast } from "sonner";
import { globalLoading } from "@/components/custom/global-loading";

/**
 * 消息提示
 */
export const message = {
  error: (content: string) => {
    console.error(`[Error] ${content}`);
    toast.error(content);
  },
  success: (content: string) => {
    console.log(`[Success] ${content}`);
    toast.success(content);
  },
  warning: (content: string) => {
    console.warn(`[Warning] ${content}`);
    toast.warning(content);
  },
  warn: (content: string) => {
    console.warn(`[Warning] ${content}`);
    toast.warning(content);
  },
  info: (content: string) => {
    console.info(`[Info] ${content}`);
    toast.info(content);
  },
};

/**
 * 通知提示
 * 注意：为了避免引入antd依赖，这里简单实现了一个通知提示接口
 * 实际项目中可以替换为UI库的通知组件，如 antd 的 notification
 */
export const notification = {
  open: (config: { message: string; description?: string }) => {
    console.info(`[Notification] ${config.message}: ${config.description || ''}`);
    toast(config.message, {
      description: config.description,
    });
  },
  success: (config: { message: string; description?: string }) => {
    console.log(`[Success Notification] ${config.message}: ${config.description || ''}`);
    toast.success(config.message, {
      description: config.description,
    });
  },
  error: (config: { message: string; description?: string }) => {
    console.error(`[Error Notification] ${config.message}: ${config.description || ''}`);
    toast.error(config.message, {
      description: config.description,
    });
  },
  info: (config: { message: string; description?: string }) => {
    console.info(`[Info Notification] ${config.message}: ${config.description || ''}`);
    toast.info(config.message, {
      description: config.description,
    });
  },
  warning: (config: { message: string; description?: string }) => {
    console.warn(`[Warning Notification] ${config.message}: ${config.description || ''}`);
    toast.warning(config.message, {
      description: config.description,
    });
  },
};

/**
 * 全局加载状态管理
 */
export const loading = {
  /**
   * 计数器，用于跟踪当前活跃的请求数量
   */
  counter: 0,

  /**
   * 显示加载中状态
   * @param text 加载提示文本
   */
  show: (text?: string) => {
    if (loading.counter === 0) {
      console.log(`[Loading] ${text || '加载中...'}`);
      globalLoading.show(text);
    }
    loading.counter++;
  },

  /**
   * 隐藏加载中状态
   */
  hide: () => {
    loading.counter = Math.max(0, loading.counter - 1);
    if (loading.counter === 0) {
      console.log('[Loading] 已完成');
      globalLoading.hide();
    }
  },

  /**
   * 重置加载状态计数器
   */
  reset: () => {
    loading.counter = 0;
    console.log('[Loading] 已重置');
    globalLoading.hide();
  }
};

/**
 * 生成唯一请求ID
 * 用于标识和跟踪请求
 */
export const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`;
};

/**
 * 格式化错误信息
 * @param error 错误对象
 * @param fallbackMessage 默认错误信息
 */
export const formatErrorMessage = (error: any, fallbackMessage: string = '请求失败'): string => {
  if (!error) return fallbackMessage;
  
  // 处理不同类型的错误
  if (typeof error === 'string') return error;
  
  // 处理业务错误
  if (error.info?.errorMessage) return error.info.errorMessage;
  
  // 处理HTTP错误
  if (error.response?.data) {
    const { message, msg, errorMessage } = error.response.data;
    if (message) return message;
    if (msg) return msg;
    if (errorMessage) return errorMessage;
  }
  
  if (error.response?.statusText) {
    return `${error.response.status || ''}: ${error.response.statusText}`;
  }
  
  // 处理一般错误
  if (error.message) return error.message;
  
  return fallbackMessage;
}; 