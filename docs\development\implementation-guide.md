# 实施指南

本文档提供了基于项目开发规范的具体实施步骤和最佳实践，帮助团队快速上手并应用开发规范。

## ⚠️ 重要提醒

**在开始任何开发工作前，请务必阅读并遵循 [MCP工具使用指南](./mcp-tools-guide.md) 中的规范要求。**

本项目已集成了完整的MCP（Model Context Protocol）工具链，开发者应该合理使用这些工具来提高开发效率和代码质量。

## 🚀 快速开始

### 1.1 环境准备
确保你的开发环境已经配置了以下工具：
- Node.js 18+
- TypeScript 5.0+
- ESLint + Prettier
- VS Code（推荐）+ 相关扩展

### 1.2 MCP工具集成验证
在开始开发前，请验证MCP工具是否正确配置：

1. **检查MCP配置文件**：确认配置包含所有必要的服务器配置
2. **验证工具可用性**：确保可以正常使用context7、filesystem、memory等工具
3. **阅读使用规范**：详细阅读MCP工具使用指南

**MCP工具使用原则**：
- 🎯 **优先使用MCP工具**：充分利用已配置的工具链
- 🔄 **遵循工具规范**：按照使用规范合理选择和组合工具
- 📝 **记录重要信息**：使用memory工具保存项目相关的重要发现
- 🤝 **用户交互确认**：重要操作使用feedback-enhanced工具确认

### 1.3 项目结构理解
```
project-root/
├── components/
│   ├── ui/                     # shadcn/ui 基础组件
│   ├── common-custom/          # 通用业务组件
│   ├── project-custom/         # 项目特定组件
│   └── component-detail/       # 预览系统
├── app/examples/              # 组件示例页面
├── types/                     # 全局类型定义
└── docs/                      # 文档和模板
```

## 🏗️ 创建新组件

### 2.1 开发前的MCP工具准备
在开始组件开发前，建议使用MCP工具进行准备工作：

```javascript
// 1. 查阅现有组件，了解项目结构
search_files_filesystem({
  path: "components",
  pattern: "*.tsx",
  excludePatterns: ["node_modules"]
})

// 2. 使用context7搜索相似组件实现
resolve_library_id("react component patterns")
get_library_docs("/facebook/react", {
  topic: "component design patterns",
  tokens: 5000
})

// 3. 检查可复用的现有组件
view({
  path: "components/common-custom",
  type: "directory"
})

// 4. 使用memory记录设计决策
create_entities_memory({
  entities: [{
    name: "new-component-design",
    entityType: "design_decision",
    observations: ["组件功能需求", "设计约束", "技术选型"]
  }]
})
```

### 2.2 判断组件类型和分类
在开始开发前，需要确定组件的类型和分类：

**组件类型判断**：
- **单文件组件**：功能相对简单，代码量 < 200 行，类型定义 < 50 行
- **复杂组件**：包含多个子组件，代码量 > 200 行，需要多个文件组织

**组件分类选择**：
- **common-custom**：通用业务组件，无项目特定依赖，可跨项目复用
- **project-custom**：集成项目特定依赖（如hooks、providers、next/navigation等）
- **ui**：基础UI组件（通常基于shadcn/ui，不建议直接修改）

### 2.3 单文件组件开发步骤

#### 步骤1：创建组件文件
```typescript
// 使用save-file工具创建组件文件
// components/common-custom/my-component.tsx

"use client"

import { ReactNode } from "react"
import { cn } from "@/lib/utils"

// 类型定义直接在组件文件内
export interface MyComponentProps {
  /**
   * 组件标题
   */
  title: string
  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean
  /**
   * 自定义类名
   */
  className?: string
}

export function MyComponent({
  title,
  disabled = false,
  className,
}: MyComponentProps) {
  return (
    <div className={cn("my-component", className)}>
      <h3>{title}</h3>
      {/* 组件实现 */}
    </div>
  )
}
```

#### 步骤2：创建示例页面
```typescript
// app/examples/my-component-example/page.tsx
"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { MyComponent } from "@/components/common-custom/my-component"
import { allExamples } from "./examples"

export default function MyComponentPreview() {
  return (
    <ComponentPreviewContainer
      title="我的组件 MyComponent"
      description="组件功能描述"
      whenToUse="何时使用这个组件"
      examples={allExamples}
      apiDocs={<MyComponentApiDocs />}
    />
  );
}

function MyComponentApiDocs() {
  // API文档实现
}
```

#### 步骤3：创建示例代码
```typescript
// app/examples/my-component-example/examples/index.ts
export const basicExample = {
  id: "basic-example",
  title: "基础用法",
  description: "组件的基本使用方式",
  code: `
import React from "react";
import { MyComponent } from "@/components/common-custom/my-component";

function BasicExample() {
  return <MyComponent title="示例标题" />;
}

render(<BasicExample />);
  `,
  scope: { MyComponent, React },
}

export const allExamples = [basicExample]
```

#### 步骤4：配置导航
在导航配置中添加新组件：
```typescript
{
  title: "我的组件",
  href: "/examples/my-component-example",
  isNew: true,
}
```

### 2.4 复杂组件开发步骤

#### 步骤1：创建组件目录结构
```javascript
// 使用filesystem工具创建复杂组件目录结构
create_directory_filesystem({
  path: "components/common-custom/my-complex-component"
})

// 批量创建必要文件
const files = [
  "components/common-custom/my-complex-component/index.tsx",
  "components/common-custom/my-complex-component/types.ts",
  "components/common-custom/my-complex-component/my-complex-component.tsx"
]
```

#### 步骤2：定义类型
```typescript
// types.ts
export interface MyComplexComponentProps {
  data: any[];
  onItemClick?: (item: any) => void;
  className?: string;
}
```

#### 步骤3：实现组件
```typescript
// my-complex-component.tsx
import { MyComplexComponentProps } from "./types"

export function MyComplexComponent({ data, onItemClick, className }: MyComplexComponentProps) {
  // 组件实现
}
```

#### 步骤4：创建导出文件
```typescript
// index.tsx
export { MyComplexComponent } from "./my-complex-component"
export * from "./types"
```

## 📋 质量检查清单

### 组件开发完成检查
- [ ] 组件具有完整的TypeScript类型定义
- [ ] 所有属性都有JSDoc注释
- [ ] 组件支持className属性
- [ ] 组件具有合理的默认值
- [ ] 代码通过ESLint和TypeScript检查
- [ ] **使用memory工具记录了组件设计决策和重要信息**
- [ ] **使用feedback-enhanced工具进行了代码审核确认**

### 示例和文档检查
- [ ] 至少包含3个不同的使用示例
- [ ] 示例代码可以独立运行
- [ ] API文档完整且准确
- [ ] 包含"何时使用"的说明
- [ ] 在导航中正确配置
- [ ] **使用context7工具验证了相关组件模式**
- [ ] **使用filesystem工具确保了文件结构的一致性**

### 用户体验检查
- [ ] 组件在不同屏幕尺寸下正常显示
- [ ] 组件支持键盘导航（如果适用）
- [ ] 组件具有合适的焦点状态
- [ ] 组件支持暗色模式（如果适用）
- [ ] **使用playwright工具进行了端到端测试验证**

### MCP工具使用检查
- [ ] **遵循了MCP工具使用规范**
- [ ] **合理使用了批量操作提高效率**
- [ ] **重要操作前使用了用户确认**
- [ ] **将重要发现记录到了memory系统**
- [ ] **使用了结构化思考解决复杂问题**

## 🛠️ 开发工具和脚本

### 推荐的VS Code扩展
- TypeScript Importer
- Tailwind CSS IntelliSense
- ES7+ React/Redux/React-Native snippets
- Auto Rename Tag

### 有用的开发脚本
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "type-check": "tsc --noEmit",
    "lint": "eslint . --ext .ts,.tsx",
    "lint:fix": "eslint . --ext .ts,.tsx --fix",
    "format": "prettier --write ."
  }
}
```

## 📝 总结

通过遵循这个实施指南和MCP工具使用规范，你可以：

1. **高效开发**：充分利用MCP工具链提高开发效率
2. **质量保证**：通过工具验证确保代码质量
3. **知识积累**：使用memory工具积累项目知识
4. **用户体验**：通过feedback工具确保用户满意度

**记住**：始终优先使用MCP工具，遵循使用规范，创建高质量、一致性的组件。
