# 动效与过渡系统

动效是现代用户界面的重要组成部分，能够增强用户体验、提供视觉反馈并引导用户注意力。本文档规范了项目中动画和过渡效果的设计原则、类型和实现方法。

## 动效设计原则

### 1. 目的性

- 每个动效都应有明确的功能目的，不为动画而动画
- 动效应增强用户理解，提供状态变化的视觉反馈
- 动效应引导用户关注重要内容或操作流程

### 2. 自然性

- 动效应模拟现实世界的物理特性，符合用户直觉
- 运动应遵循加速和减速的自然规律
- 元素进入应当有恰当的起点，离开应当有合理的去向

### 3. 一致性

- 相似的交互应使用相似的动效
- 保持整个应用中动效风格的一致性
- 统一动效的时长、缓动函数和触发方式

## 动效类型

### 微交互动效

微交互是对用户操作的即时视觉反馈，通常触发快速、轻量的动画。

```jsx
// 按钮悬停效果
<Button
  className="transition-all duration-200 hover:scale-105"
>
  提交
</Button>

// 开关状态变化
<Switch
  checked={isChecked}
  onCheckedChange={setIsChecked}
  className="transition-colors duration-200"
/>
```

### 状态变化动效

展示元素状态变化的动画，如加载、成功、错误等状态的过渡。

```jsx
// 加载状态动画
<Button disabled={isLoading}>
  {isLoading ? (
    <span className="flex items-center">
      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
      加载中...
    </span>
  ) : (
    "提交"
  )}
</Button>

// 成功状态动画
function SuccessAnimation() {
  const [success, setSuccess] = useState(false)
  
  return (
    <div>
      <Button onClick={() => setSuccess(true)}>
        完成操作
      </Button>
      
      <div className="relative mt-4 h-8">
        <CheckCircle 
          className={`absolute h-8 w-8 text-success-600 transition-all duration-500 ${
            success 
              ? "opacity-100 scale-100" 
              : "opacity-0 scale-0"
          }`}
        />
      </div>
    </div>
  )
}
```

### 导航过渡

在页面或视图切换时提供流畅的过渡效果。

```jsx
// 使用framer-motion实现页面过渡
import { motion } from "framer-motion"

const pageVariants = {
  initial: {
    opacity: 0,
    y: 10,
  },
  animate: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: "easeOut",
    },
  },
  exit: {
    opacity: 0,
    y: -10,
    transition: {
      duration: 0.2,
      ease: "easeIn",
    },
  },
}

function AnimatedPage({ children }) {
  return (
    <motion.div
      initial="initial"
      animate="animate"
      exit="exit"
      variants={pageVariants}
    >
      {children}
    </motion.div>
  )
}
```

### 内容变化动效

当内容发生添加、移除或更新时的动画效果。

```jsx
// 列表项添加/删除动画
import { motion, AnimatePresence } from "framer-motion"

function AnimatedList({ items }) {
  return (
    <ul className="space-y-2">
      <AnimatePresence>
        {items.map((item) => (
          <motion.li
            key={item.id}
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="bg-card p-4 rounded-md shadow-sm"
          >
            {item.content}
          </motion.li>
        ))}
      </AnimatePresence>
    </ul>
  )
}
```

### 引导动效

引导用户注意力的动画，通常用于指引用户完成任务或关注重要信息。

```jsx
// 聚焦引导动画
function FocusAttentionEffect({ children, isHighlighted }) {
  return (
    <div className={`relative rounded-lg p-4 transition-all duration-300 ${
      isHighlighted ? "bg-primary/5 shadow-md" : "bg-background"
    }`}>
      {isHighlighted && (
        <div className="absolute inset-0 rounded-lg border-2 border-primary animate-pulse" />
      )}
      {children}
    </div>
  )
}
```

## 动效时间与缓动

### 标准时长

定义一组标准的动画持续时间，确保整个应用的一致性。

```jsx
// 动效时长变量
const durations = {
  fastest: "50ms",  // 极快，用于微交互
  fast: "150ms",    // 快速，用于小元素、状态变化
  normal: "250ms",  // 标准，用于大多数UI元素
  slow: "350ms",    // 慢速，用于大型元素、页面过渡
  slowest: "500ms", // 极慢，用于复杂动画、特殊强调
}

// 示例使用
<div className={`transition-opacity duration-${durations.normal}`}>
  {/* 内容 */}
</div>
```

### 缓动函数

定义标准的缓动函数，模拟自然的物理运动。

```jsx
// 缓动函数变量
const easings = {
  // 默认缓动，适用于大多数场景
  default: "cubic-bezier(0.4, 0, 0.2, 1)",
  
  // 进入缓动，从慢到快
  in: "cubic-bezier(0.4, 0, 1, 1)",
  
  // 退出缓动，从快到慢
  out: "cubic-bezier(0, 0, 0.2, 1)",
  
  // 弹性缓动，带有轻微回弹效果
  elastic: "cubic-bezier(0.68, -0.6, 0.32, 1.6)",
}

// 示例使用
<button 
  className={`transition-transform duration-${durations.normal} ease-${easings.elastic} hover:scale-105`}
>
  弹性缩放按钮
</button>
```

## 基础动效组件

### 过渡容器

创建标准的过渡容器组件，实现一致的动画效果。

```jsx
// components/custom/transition-container.tsx
import { ReactNode } from "react"
import { Transition } from "@headlessui/react"

type TransitionType = "fade" | "scale" | "slide-up" | "slide-down" | "slide-left" | "slide-right"

interface TransitionContainerProps {
  show: boolean
  type?: TransitionType
  duration?: "fastest" | "fast" | "normal" | "slow" | "slowest"
  children: ReactNode
}

export function TransitionContainer({
  show,
  type = "fade",
  duration = "normal",
  children,
}: TransitionContainerProps) {
  // 根据类型设置进入和离开的类名
  const getTransitionClasses = () => {
    const durationClass = {
      fastest: "duration-50",
      fast: "duration-150",
      normal: "duration-250",
      slow: "duration-350",
      slowest: "duration-500",
    }[duration]
    
    const transitions = {
      fade: {
        enter: "transition-opacity ease-out",
        enterFrom: "opacity-0",
        enterTo: "opacity-100",
        leave: "transition-opacity ease-in",
        leaveFrom: "opacity-100",
        leaveTo: "opacity-0",
      },
      scale: {
        enter: "transition-transform ease-out",
        enterFrom: "scale-95 opacity-0",
        enterTo: "scale-100 opacity-100",
        leave: "transition-transform ease-in",
        leaveFrom: "scale-100 opacity-100",
        leaveTo: "scale-95 opacity-0",
      },
      "slide-up": {
        enter: "transition-transform ease-out",
        enterFrom: "translate-y-4 opacity-0",
        enterTo: "translate-y-0 opacity-100",
        leave: "transition-transform ease-in",
        leaveFrom: "translate-y-0 opacity-100",
        leaveTo: "translate-y-4 opacity-0",
      },
      "slide-down": {
        enter: "transition-transform ease-out",
        enterFrom: "translate-y-[-10px] opacity-0",
        enterTo: "translate-y-0 opacity-100",
        leave: "transition-transform ease-in",
        leaveFrom: "translate-y-0 opacity-100",
        leaveTo: "translate-y-[-10px] opacity-0",
      },
      "slide-left": {
        enter: "transition-transform ease-out",
        enterFrom: "translate-x-4 opacity-0",
        enterTo: "translate-x-0 opacity-100",
        leave: "transition-transform ease-in",
        leaveFrom: "translate-x-0 opacity-100",
        leaveTo: "translate-x-4 opacity-0",
      },
      "slide-right": {
        enter: "transition-transform ease-out",
        enterFrom: "translate-x-[-10px] opacity-0",
        enterTo: "translate-x-0 opacity-100",
        leave: "transition-transform ease-in",
        leaveFrom: "translate-x-0 opacity-100",
        leaveTo: "translate-x-[-10px] opacity-0",
      },
    }[type]
    
    return {
      ...transitions,
      enter: `${transitions.enter} ${durationClass}`,
      leave: `${transitions.leave} ${durationClass}`,
    }
  }
  
  const classes = getTransitionClasses()
  
  return (
    <Transition
      show={show}
      enter={classes.enter}
      enterFrom={classes.enterFrom}
      enterTo={classes.enterTo}
      leave={classes.leave}
      leaveFrom={classes.leaveFrom}
      leaveTo={classes.leaveTo}
    >
      {children}
    </Transition>
  )
}
```

### 动画按钮

创建带有动画效果的按钮组件。

```jsx
// components/custom/animated-button.tsx
import { ButtonHTMLAttributes } from "react"
import { Button } from "@/components/ui/button"
import { Loader2 } from "lucide-react"

interface AnimatedButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean
  loadingText?: string
  animateScale?: boolean
  children: React.ReactNode
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
}

export function AnimatedButton({
  isLoading = false,
  loadingText,
  animateScale = true,
  children,
  variant = "default",
  size = "default",
  ...props
}: AnimatedButtonProps) {
  return (
    <Button
      variant={variant}
      size={size}
      disabled={isLoading || props.disabled}
      className={`relative ${
        animateScale ? "transition-transform duration-150 hover:scale-105 active:scale-95" : ""
      }`}
      {...props}
    >
      {isLoading ? (
        <span className="flex items-center">
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          {loadingText || children}
        </span>
      ) : (
        children
      )}
    </Button>
  )
}
```

### 动画图标

创建带有动画效果的图标组件。

```jsx
// components/custom/animated-icon.tsx
import { LucideIcon } from "lucide-react"

type AnimationType = "spin" | "pulse" | "bounce" | "ping"

interface AnimatedIconProps {
  icon: LucideIcon
  animation: AnimationType
  size?: number
  color?: string
  className?: string
}

export function AnimatedIcon({
  icon: Icon,
  animation,
  size = 24,
  color,
  className = "",
}: AnimatedIconProps) {
  const animationClass = {
    spin: "animate-spin",
    pulse: "animate-pulse",
    bounce: "animate-bounce",
    ping: "animate-ping",
  }[animation]
  
  return (
    <Icon
      size={size}
      color={color}
      className={`${animationClass} ${className}`}
    />
  )
}

// 使用示例
<AnimatedIcon 
  icon={Refresh} 
  animation="spin" 
  size={20}
  color="currentColor"
/>
```

## Tailwind动画预设

使用Tailwind CSS预设的动画类实现常见动效。

```jsx
// tailwind.config.js中的自定义动画配置
module.exports = {
  theme: {
    extend: {
      animation: {
        'fade-in': 'fadeIn 0.3s ease-out',
        'fade-out': 'fadeOut 0.2s ease-in',
        'slide-in-right': 'slideInRight 0.3s ease-out',
        'slide-out-right': 'slideOutRight 0.2s ease-in',
        'slide-in-bottom': 'slideInBottom 0.3s ease-out',
        'slide-out-bottom': 'slideOutBottom 0.2s ease-in',
        'scale-in': 'scaleIn 0.2s ease-out',
        'scale-out': 'scaleOut 0.15s ease-in',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeOut: {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        slideInRight: {
          '0%': { transform: 'translateX(-10px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        slideOutRight: {
          '0%': { transform: 'translateX(0)', opacity: '1' },
          '100%': { transform: 'translateX(-10px)', opacity: '0' },
        },
        slideInBottom: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideOutBottom: {
          '0%': { transform: 'translateY(0)', opacity: '1' },
          '100%': { transform: 'translateY(10px)', opacity: '0' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        scaleOut: {
          '0%': { transform: 'scale(1)', opacity: '1' },
          '100%': { transform: 'scale(0.95)', opacity: '0' },
        },
      },
    },
  },
}

// 使用Tailwind动画类
<div className="animate-fade-in">
  渐入内容
</div>

<div className="animate-slide-in-bottom">
  从底部滑入
</div>

<div className="animate-scale-in">
  缩放显示
</div>
```

## Framer Motion集成

对于更复杂的动画，使用Framer Motion库提供高级控制。

```jsx
// 基于Framer Motion的高级动画组件
import { motion, AnimatePresence } from "framer-motion"

// 可重用的动画变体
const fadeInUpVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: "easeOut",
    },
  },
  exit: {
    opacity: 0,
    y: 20,
    transition: {
      duration: 0.2,
      ease: "easeIn",
    },
  },
}

// 可重用的动画变体
const staggerChildrenVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
}

// 动画列表示例
function AnimatedList({ items }) {
  return (
    <motion.ul
      variants={staggerChildrenVariants}
      initial="hidden"
      animate="visible"
      className="space-y-3"
    >
      <AnimatePresence>
        {items.map((item) => (
          <motion.li
            key={item.id}
            variants={fadeInUpVariants}
            exit="exit"
            layout
            className="bg-card p-4 rounded-md shadow-sm"
          >
            {item.content}
          </motion.li>
        ))}
      </AnimatePresence>
    </motion.ul>
  )
}

// 页面过渡示例
function AnimatedPage({ children }) {
  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={router.pathname}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  )
}
```

## 常见UI组件动效

### 模态框动效

```jsx
import { Dialog, Transition } from "@headlessui/react"
import { Fragment } from "react"

function AnimatedDialog({ isOpen, onClose, title, children }) {
  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title
                  as="h3"
                  className="text-lg font-medium leading-6 text-gray-900"
                >
                  {title}
                </Dialog.Title>
                <div className="mt-2">
                  {children}
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}
```

### 下拉菜单动效

```jsx
import { Menu, Transition } from "@headlessui/react"
import { Fragment } from "react"

function AnimatedDropdown({ button, items }) {
  return (
    <Menu as="div" className="relative inline-block text-left">
      <Menu.Button>{button}</Menu.Button>
      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="absolute right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          {items.map((item, index) => (
            <Menu.Item key={index}>
              {({ active }) => (
                <button
                  className={`${
                    active ? "bg-primary text-white" : "text-gray-900"
                  } group flex w-full items-center rounded-md px-2 py-2 text-sm`}
                  onClick={item.onClick}
                >
                  {item.icon && (
                    <item.icon
                      className="mr-2 h-5 w-5"
                      aria-hidden="true"
                    />
                  )}
                  {item.label}
                </button>
              )}
            </Menu.Item>
          ))}
        </Menu.Items>
      </Transition>
    </Menu>
  )
}
```

### 通知提示动效

```jsx
import { Transition } from "@headlessui/react"
import { CheckCircle, XCircle, Info, AlertTriangle, X } from "lucide-react"
import { useState, useEffect } from "react"

type ToastType = "success" | "error" | "info" | "warning"

interface ToastProps {
  type: ToastType
  message: string
  duration?: number
  onClose: () => void
}

function Toast({ type, message, duration = 5000, onClose }: ToastProps) {
  const [show, setShow] = useState(true)
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setShow(false)
      setTimeout(onClose, 300) // 动画完成后再移除
    }, duration)
    
    return () => clearTimeout(timer)
  }, [duration, onClose])
  
  const iconMap = {
    success: <CheckCircle className="h-5 w-5 text-success-600" />,
    error: <XCircle className="h-5 w-5 text-destructive" />,
    info: <Info className="h-5 w-5 text-primary" />,
    warning: <AlertTriangle className="h-5 w-5 text-warning-600" />,
  }
  
  const bgColorMap = {
    success: "bg-success-50 border-success-200",
    error: "bg-destructive-50 border-destructive-200",
    info: "bg-primary-50 border-primary-200",
    warning: "bg-warning-50 border-warning-200",
  }
  
  return (
    <Transition
      show={show}
      enter="transform transition ease-out duration-300"
      enterFrom="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
      enterTo="translate-y-0 opacity-100 sm:translate-x-0"
      leave="transform transition ease-in duration-200"
      leaveFrom="translate-y-0 opacity-100 sm:translate-x-0"
      leaveTo="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
    >
      <div className={`max-w-sm w-full shadow-lg rounded-lg pointer-events-auto border ${bgColorMap[type]}`}>
        <div className="p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              {iconMap[type]}
            </div>
            <div className="ml-3 w-0 flex-1 pt-0.5">
              <p className="text-sm font-medium text-gray-900">{message}</p>
            </div>
            <div className="ml-4 flex-shrink-0 flex">
              <button
                className="bg-transparent rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none"
                onClick={() => {
                  setShow(false)
                  setTimeout(onClose, 300)
                }}
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  )
}
```

### 折叠面板动效

```jsx
import { Disclosure, Transition } from "@headlessui/react"
import { ChevronUp } from "lucide-react"

function AnimatedAccordion({ items }) {
  return (
    <div className="space-y-2">
      {items.map((item, index) => (
        <Disclosure key={index}>
          {({ open }) => (
            <>
              <Disclosure.Button className="flex w-full justify-between rounded-lg bg-primary-50 px-4 py-3 text-left text-sm font-medium text-primary hover:bg-primary-100 focus:outline-none focus-visible:ring focus-visible:ring-primary-500 focus-visible:ring-opacity-75">
                <span>{item.title}</span>
                <ChevronUp
                  className={`${
                    open ? "rotate-180 transform" : ""
                  } h-5 w-5 text-primary-500 transition-transform duration-200`}
                />
              </Disclosure.Button>
              <Transition
                enter="transition duration-100 ease-out"
                enterFrom="transform scale-95 opacity-0"
                enterTo="transform scale-100 opacity-100"
                leave="transition duration-75 ease-out"
                leaveFrom="transform scale-100 opacity-100"
                leaveTo="transform scale-95 opacity-0"
              >
                <Disclosure.Panel className="px-4 pt-4 pb-2 text-sm text-gray-500">
                  {item.content}
                </Disclosure.Panel>
              </Transition>
            </>
          )}
        </Disclosure>
      ))}
    </div>
  )
}
```

## 性能优化

### 动画性能考虑

确保动画不会影响应用性能的关键因素和最佳实践。

```jsx
// 1. 使用CSS属性动画
// 优先使用不触发重排的CSS属性：transform和opacity
<div className="transition-transform duration-300 hover:translate-y-[-4px]">
  高性能悬停效果
</div>

// 2. 启用GPU加速
<div className="transition-transform duration-300 hover:translate-y-[-4px] will-change-transform">
  GPU加速的动画
</div>

// 3. 限制同时执行的动画数量
function OptimizedListAnimation({ items }) {
  // 只对可见项应用动画
  const [visibleItems, setVisibleItems] = useState([])
  
  useEffect(() => {
    // 分批显示项目，避免同时执行太多动画
    const showItems = async () => {
      const batchSize = 5
      for (let i = 0; i < items.length; i += batchSize) {
        const batch = items.slice(i, i + batchSize)
        setVisibleItems(prev => [...prev, ...batch])
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }
    
    showItems()
  }, [items])
  
  return (
    <ul>
      {items.map(item => (
        <motion.li
          key={item.id}
          initial={{ opacity: 0 }}
          animate={visibleItems.includes(item) ? { opacity: 1 } : {}}
        >
          {item.content}
        </motion.li>
      ))}
    </ul>
  )
}
```

### 动画可访问性

确保动画不会对可访问性产生负面影响，特别是对于有运动敏感症的用户。

```jsx
// 根据用户偏好减少动画
function AnimationPreference({ children }) {
  // 检测用户是否偏好减少动画
  const prefersReducedMotion = 
    typeof window !== "undefined" && 
    window.matchMedia("(prefers-reduced-motion: reduce)").matches
  
  // 动画变体
  const variants = {
    default: {
      hidden: { opacity: 0, y: 10 },
      visible: { opacity: 1, y: 0, transition: { duration: 0.3 } },
    },
    reduced: {
      hidden: { opacity: 0 },
      visible: { opacity: 1, transition: { duration: 0.1 } },
    },
  }
  
  // 选择适当的变体
  const currentVariant = prefersReducedMotion ? variants.reduced : variants.default
  
  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={currentVariant}
    >
      {children}
    </motion.div>
  )
}
```

## 实际应用示例

### 卡片悬停效果

```jsx
// 交互式卡片组件
function InteractiveCard({ title, description, icon: Icon, onClick }) {
  return (
    <div
      onClick={onClick}
      className="group relative overflow-hidden rounded-lg border bg-background p-6 text-foreground shadow-sm transition-all duration-300 hover:shadow-md hover:-translate-y-1 cursor-pointer"
    >
      <div className="flex items-start">
        {Icon && (
          <div className="mr-4 rounded-full bg-primary/10 p-3 transition-colors duration-300 group-hover:bg-primary/20">
            <Icon className="h-6 w-6 text-primary transition-transform duration-300 group-hover:scale-110" />
          </div>
        )}
        <div>
          <h3 className="text-lg font-medium">{title}</h3>
          <p className="mt-2 text-sm text-muted-foreground">{description}</p>
        </div>
      </div>
      <div className="absolute bottom-0 left-0 h-1 w-0 bg-primary transition-all duration-300 group-hover:w-full"></div>
    </div>
  )
}
```

### 页面加载动效

```jsx
// 页面加载动画
function PageLoadAnimation({ children, isLoading }) {
  return (
    <AnimatePresence mode="wait">
      {isLoading ? (
        <motion.div
          key="loading"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="flex h-[50vh] items-center justify-center"
        >
          <div className="relative h-20 w-20">
            <div className="absolute inset-0 rounded-full border-4 border-primary-100"></div>
            <div className="absolute inset-0 rounded-full border-4 border-primary border-t-transparent animate-spin"></div>
          </div>
        </motion.div>
      ) : (
        <motion.div
          key="content"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1, duration: 0.3 }}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  )
}
```

### 图表动画

```jsx
// 图表动画
import { Bar } from "react-chartjs-2"

function AnimatedBarChart({ data }) {
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    animation: {
      duration: 1000, // 动画持续时间
      easing: "easeOutQuart", // 缓动函数
      delay: (context) => {
        // 为每个条形添加延迟，创建顺序动画
        return context.dataIndex * 100
      },
    },
    plugins: {
      legend: {
        position: "top",
      },
      title: {
        display: true,
        text: "销售数据",
      },
    },
  }
  
  return (
    <div className="h-64 w-full">
      <Bar data={data} options={options} />
    </div>
  )
}
```

### 标签页切换动效

```jsx
import { Tab } from "@headlessui/react"
import { motion, AnimatePresence } from "framer-motion"

function AnimatedTabs({ tabs }) {
  return (
    <Tab.Group>
      <Tab.List className="flex space-x-1 rounded-xl bg-secondary p-1">
        {tabs.map((tab) => (
          <Tab
            key={tab.label}
            className={({ selected }) =>
              `relative w-full rounded-lg py-2.5 text-sm font-medium transition-colors duration-200 ${
                selected 
                  ? "bg-white text-primary shadow" 
                  : "text-muted-foreground hover:bg-white/[0.12] hover:text-foreground"
              }`
            }
          >
            {({ selected }) => (
              <>
                <span>{tab.label}</span>
                {selected && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute inset-0 rounded-lg bg-white"
                    style={{ zIndex: -1 }}
                  />
                )}
              </>
            )}
          </Tab>
        ))}
      </Tab.List>
      <Tab.Panels className="mt-2">
        <AnimatePresence mode="wait">
          {tabs.map((tab, idx) => (
            <Tab.Panel
              key={idx}
              className="rounded-xl bg-white p-3"
            >
              <motion.div
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -5 }}
                transition={{ duration: 0.2 }}
              >
                {tab.content}
              </motion.div>
            </Tab.Panel>
          ))}
        </AnimatePresence>
      </Tab.Panels>
    </Tab.Group>
  )
}
```

## 最佳实践

### 动效使用推荐做法

1. **目的优先**：确保每个动效都有明确的目的，增强用户体验
2. **适度使用**：过多或过于复杂的动效会分散用户注意力
3. **响应速度**：微交互反馈应当即时，不要有明显延迟
4. **渐进增强**：首先确保功能可用，然后再添加动效
5. **一致性**：在整个应用中保持动效风格的一致性

### 动效使用避免事项

1. **避免过度动画**：过多的动画会导致视觉疲劳和注意力分散
2. **避免过长时长**：大多数UI动画应保持在300ms以下，确保流畅性
3. **避免违反用户期望**：动效应该符合用户的心理模型和物理规律
4. **避免同时进行多个动画**：可能导致性能问题和视觉混乱
5. **避免忽视可访问性**：必须考虑偏好减少动画的用户