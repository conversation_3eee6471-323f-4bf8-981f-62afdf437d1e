# 交互设计

交互设计是用户体验的核心部分，决定着用户如何与界面进行互动。本文档规范了动画、过渡效果和状态变化的设计规则，确保整个应用的交互体验一致、自然且符合人体工程学。

## 交互设计原则

我们的交互设计遵循以下核心原则：

### 1. 即时反馈

每个用户操作都应该得到明确的视觉反馈，告诉用户操作已被接收并正在处理。

- 按钮点击时有明显的状态变化
- 表单提交后显示加载状态
- 操作完成后提供成功/失败的反馈

### 2. 平滑过渡

状态变化和页面切换应该平滑自然，避免突兀的跳转。

- 组件进入/离开时使用淡入淡出效果
- 内容变化时使用适当的过渡动画
- 避免生硬的状态切换

### 3. 自然节奏

动画和过渡的时间和节奏应该模仿自然界的运动规律，给用户带来舒适的体验。

- 使用恰当的缓动函数模拟自然运动
- 短交互使用较快的过渡（150-250ms）
- 重要过渡使用较慢的动画（300-500ms）

### 4. 测不准交互原则

交互设计应该引导用户，但不应限制用户，要在引导性和自由度之间找到平衡。

- 提供清晰的操作路径，但允许用户自由探索
- 危险操作增加确认步骤，但不过度打断用户流程
- 自动保存用户进度，减少数据丢失风险

## 动画系统

### 动画时长变量

我们定义了一组标准的动画时长变量，确保整个应用中的动画节奏一致。

```css
:root {
  --animation-fast: 150ms;    /* 微交互，如按钮点击、开关切换 */
  --animation-normal: 250ms;  /* 标准交互，如下拉菜单、工具提示 */
  --animation-slow: 350ms;    /* 内容过渡，如模态框、抽屉 */
  --animation-slower: 500ms;  /* 页面级过渡，如页面切换 */
}
```

### 缓动函数

缓动函数决定了动画的节奏和感觉，我们使用以下标准缓动函数：

```css
:root {
  /* 标准缓动 - 自然感觉的基础缓动 */
  --ease-standard: cubic-bezier(0.2, 0, 0, 1);
  
  /* 进入缓动 - 元素进入屏幕时使用 */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  
  /* 退出缓动 - 元素离开屏幕时使用 */
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  
  /* 弹性缓动 - 需要活泼感的交互 */
  --ease-bounce: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
```

### 常用动画预设

```css
/* 淡入 */
.fade-in {
  animation: fadeIn var(--animation-normal) var(--ease-out) forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 滑入（从下） */
.slide-in-bottom {
  animation: slideInBottom var(--animation-normal) var(--ease-out) forwards;
}

@keyframes slideInBottom {
  from { 
    transform: translateY(20px); 
    opacity: 0;
  }
  to { 
    transform: translateY(0); 
    opacity: 1;
  }
}

/* 缩放进入 */
.scale-in {
  animation: scaleIn var(--animation-normal) var(--ease-bounce) forwards;
}

@keyframes scaleIn {
  from { 
    transform: scale(0.9); 
    opacity: 0; 
  }
  to { 
    transform: scale(1); 
    opacity: 1; 
  }
}
```

## 状态变化

### 按钮状态变化

按钮在不同状态下应有明确的视觉反馈：

```css
.button {
  transition: background-color var(--animation-fast) var(--ease-standard),
              transform var(--animation-fast) var(--ease-standard),
              opacity var(--animation-fast) var(--ease-standard);
}

.button:hover {
  background-color: var(--button-hover);
}

.button:active {
  transform: scale(0.98);
}

.button:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
```

### 表单元素状态

表单元素在各种状态下的视觉反馈：

```css
.input {
  transition: border-color var(--animation-fast) var(--ease-standard),
              box-shadow var(--animation-fast) var(--ease-standard);
}

.input:hover {
  border-color: var(--border-hover);
}

.input:focus {
  border-color: var(--ring);
  box-shadow: 0 0 0 2px var(--ring/20%);
}

.input.error {
  border-color: var(--destructive);
}

.input:disabled {
  opacity: 0.5;
  background-color: var(--muted);
  cursor: not-allowed;
}
```

## 页面过渡

### Next.js 页面过渡

使用Next.js的内置页面过渡系统，实现平滑的页面切换：

```jsx
// app/layout.tsx
import { Transition } from "@/components/transition"

export default function RootLayout({ children }) {
  return (
    <html lang="zh-CN">
      <body>
        <Transition>{children}</Transition>
      </body>
    </html>
  )
}

// components/transition.tsx
"use client"

import { motion } from "framer-motion"

export function Transition({ children }) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, ease: [0.22, 1, 0.36, 1] }}
    >
      {children}
    </motion.div>
  )
}
```

### 内容加载过渡

内容加载时的骨架屏和过渡效果：

```jsx
import { Suspense } from "react"
import { Skeleton } from "@/components/ui/skeleton"

export default function DataPage() {
  return (
    <div>
      <h1>数据页面</h1>
      <Suspense fallback={<DataCardSkeleton />}>
        <DataCard />
      </Suspense>
    </div>
  )
}

function DataCardSkeleton() {
  return (
    <div className="rounded-lg border p-4 animate-pulse">
      <Skeleton className="h-6 w-1/3 mb-4" />
      <Skeleton className="h-4 w-full mb-2" />
      <Skeleton className="h-4 w-2/3" />
    </div>
  )
}
```

## 微交互

### 工具提示 (Tooltip)

工具提示的出现和消失应平滑自然：

```jsx
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

export function TooltipDemo() {
  return (
    <TooltipProvider delayDuration={300}>
      <Tooltip>
        <TooltipTrigger>悬停查看更多</TooltipTrigger>
        <TooltipContent 
          sideOffset={5}
          className="slide-in-bottom"
        >
          <p>这是工具提示内容</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
```

### 下拉菜单 (Dropdown)

下拉菜单的展开和收起应有明确的方向性：

```jsx
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export function DropdownMenuDemo() {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger>打开菜单</DropdownMenuTrigger>
      <DropdownMenuContent 
        sideOffset={5}
        align="start"
        className="slide-in-bottom"
      >
        <DropdownMenuItem>选项1</DropdownMenuItem>
        <DropdownMenuItem>选项2</DropdownMenuItem>
        <DropdownMenuItem>选项3</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
```

## 加载状态

### 按钮加载状态

按钮在加载时应显示加载指示器，并禁用进一步交互：

```jsx
import { Button } from "@/components/ui/button"
import { Loader2 } from "lucide-react"
import { useState } from "react"

export function LoadingButtonDemo() {
  const [isLoading, setIsLoading] = useState(false)
  
  const handleClick = async () => {
    setIsLoading(true)
    // 模拟异步操作
    await new Promise(resolve => setTimeout(resolve, 2000))
    setIsLoading(false)
  }
  
  return (
    <Button 
      onClick={handleClick} 
      disabled={isLoading}
    >
      {isLoading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          处理中...
        </>
      ) : (
        "点击提交"
      )}
    </Button>
  )
}
```

### 页面加载状态

使用Next.js的loading.tsx实现页面加载状态：

```jsx
// app/dashboard/loading.tsx
import { Skeleton } from "@/components/ui/skeleton"

export default function DashboardLoading() {
  return (
    <div className="p-6 space-y-6">
      <Skeleton className="h-8 w-1/3" />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Skeleton className="h-32 rounded-lg" />
        <Skeleton className="h-32 rounded-lg" />
        <Skeleton className="h-32 rounded-lg" />
      </div>
      <Skeleton className="h-64 rounded-lg" />
    </div>
  )
}
```

## 拖拽交互

### 拖拽列表

可拖拽排序的列表实现：

```jsx
"use client"

import { useState } from "react"
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from "@dnd-kit/core"
import { arrayMove, SortableContext, sortableKeyboardCoordinates, useSortable, verticalListSortingStrategy } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"

function SortableItem({ id, content }) {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id })
  
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }
  
  return (
    <div 
      ref={setNodeRef} 
      style={style} 
      {...attributes} 
      {...listeners}
      className="p-4 mb-2 bg-card rounded-md border cursor-grab active:cursor-grabbing"
    >
      {content}
    </div>
  )
}

export function DraggableListDemo() {
  const [items, setItems] = useState([
    { id: "1", content: "项目1" },
    { id: "2", content: "项目2" },
    { id: "3", content: "项目3" },
  ])
  
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )
  
  function handleDragEnd(event) {
    const { active, over } = event
    
    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.findIndex(item => item.id === active.id)
        const newIndex = items.findIndex(item => item.id === over.id)
        
        return arrayMove(items, oldIndex, newIndex)
      })
    }
  }
  
  return (
    <DndContext 
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
    >
      <SortableContext 
        items={items.map(item => item.id)}
        strategy={verticalListSortingStrategy}
      >
        <div>
          {items.map(item => (
            <SortableItem key={item.id} id={item.id} content={item.content} />
          ))}
        </div>
      </SortableContext>
    </DndContext>
  )
}
```

## 滚动行为

### 平滑滚动

实现页内平滑滚动：

```css
html {
  scroll-behavior: smooth;
}
```

```jsx
// 平滑滚动到指定元素
function scrollToElement(elementId) {
  const element = document.getElementById(elementId)
  if (element) {
    element.scrollIntoView({ 
      behavior: "smooth",
      block: "start"
    })
  }
}
```

### 无限滚动

实现无限滚动加载内容：

```jsx
"use client"

import { useEffect, useState } from "react"
import { useInView } from "react-intersection-observer"
import { Loader2 } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

export function InfiniteScrollDemo() {
  const [items, setItems] = useState([])
  const [page, setPage] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  
  const { ref, inView } = useInView({
    threshold: 0.1,
  })
  
  const loadMoreItems = async () => {
    if (isLoading || !hasMore) return
    
    setIsLoading(true)
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const newItems = Array.from({ length: 10 }, (_, i) => ({
      id: items.length + i + 1,
      title: `项目 ${items.length + i + 1}`,
      description: `这是项目 ${items.length + i + 1} 的描述内容...`
    }))
    
    setItems(prev => [...prev, ...newItems])
    setPage(prev => prev + 1)
    setIsLoading(false)
    
    // 模拟数据到达上限
    if (page >= 5) {
      setHasMore(false)
    }
  }
  
  useEffect(() => {
    if (inView) {
      loadMoreItems()
    }
  }, [inView])
  
  return (
    <div className="space-y-6">
      {items.map(item => (
        <Card key={item.id} className="fade-in">
          <CardContent className="p-6">
            <h3 className="text-lg font-medium">{item.title}</h3>
            <p className="text-muted-foreground">{item.description}</p>
          </CardContent>
        </Card>
      ))}
      
      {hasMore && (
        <div ref={ref} className="flex justify-center p-4">
          {isLoading && (
            <div className="flex items-center">
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              加载更多...
            </div>
          )}
        </div>
      )}
      
      {!hasMore && (
        <div className="text-center text-muted-foreground py-4">
          没有更多内容了
        </div>
      )}
    </div>
  )
}
```

## 最佳实践

### 交互设计推荐做法

1. **一致性**：在相似场景下使用相同的交互模式，建立用户预期
2. **渐进增强**：基础功能无需动画也能工作，动画作为体验增强
3. **减少认知负担**：动画应帮助用户理解界面变化，而非分散注意力
4. **减少延迟感**：对于长时间操作，提供即时反馈和进度指示
5. **考虑无障碍性**：为喜欢减少动画的用户提供`prefers-reduced-motion`支持

### 动画性能优化

1. **使用CSS属性**：优先使用`transform`和`opacity`进行动画，它们不触发布局重排
2. **避免同时动画**：避免同时对大量元素应用复杂动画
3. **使用`will-change`**：对关键动画元素使用`will-change`属性提示浏览器
4. **批量DOM更新**：使用`requestAnimationFrame`批量处理DOM更新

```css
.optimized-animation {
  will-change: transform, opacity;
  transform: translateZ(0); /* 触发GPU加速 */
}
```

### 减少动画 (prefers-reduced-motion)

尊重用户对减少动画的偏好：

```css
@media (prefers-reduced-motion: reduce) {
  *, ::before, ::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .fade-in,
  .slide-in-bottom,
  .scale-in {
    animation: none !important;
    opacity: 1 !important;
    transform: none !important;
  }
}
``` 