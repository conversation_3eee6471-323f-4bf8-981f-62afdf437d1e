# 请求工具库

这个目录包含了项目中使用的请求工具库，基于 axios 实现，提供了更加便捷的请求方式和错误处理机制。

## 主要功能

- 请求和响应拦截
- 统一的错误处理
- 请求取消支持
- 类型安全的API定义
- 全局加载状态管理
- 统一的请求状态反馈
- 灵活的参数传递方式

## 使用方式

### 基本用法

```typescript
import { request } from '@/lib/request';

// GET 请求
const getData = async () => {
  const result = await request.get('/api/data');
  return result;
};

// 带参数的 GET 请求
const getUserById = async (id: number) => {
  // 方式一：使用params选项
  const user1 = await request.get('/api/users', { params: { id } });
  
  // 方式二：直接传递参数对象（会自动转换为params）
  const user2 = await request.get('/api/users', { id });
  
  return user1; // 或 user2
};

// POST 请求
const createData = async (data: any) => {
  // 直接传递数据对象作为第二个参数
  const result = await request.post('/api/data', data);
  
  // 也可以使用更明确的参数形式
  // const result = await request.post('/api/data', data, { timeout: 5000 });
  
  return result;
};

// PUT 请求
const updateData = async (id: number, data: any) => {
  // 直接传递数据对象
  const result = await request.put(`/api/data/${id}`, data);
  return result;
};

// PATCH 请求（部分更新）
const partialUpdateData = async (id: number, data: any) => {
  // 直接传递部分数据
  const result = await request.patch(`/api/data/${id}`, data);
  return result;
};

// DELETE 请求
const deleteData = async (id: number) => {
  // 直接传递ID，会自动转换为 params
  await request.delete('/api/data', { id });
  
  // 或者使用URL参数
  // await request.delete(`/api/data/${id}`);
};
```

### 在Next.js中使用

由于Next.js的服务器组件不支持直接使用浏览器API，请确保在客户端组件中使用此请求库：

```typescript
"use client";

import { request } from '@/lib/request';
import { useState, useEffect } from 'react';

export default function ClientComponent() {
  const [data, setData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await request.get('/api/data');
        setData(result);
      } catch (error) {
        console.error('获取数据失败:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, []);
  
  if (isLoading) return <div>加载中...</div>;
  
  return <div>{/* 渲染数据 */}</div>;
}
```

对于服务器组件，建议使用Next.js原生的数据获取方法：

```typescript
// 服务器组件
import { cookies } from 'next/headers';

export default async function ServerComponent() {
  // 使用fetch API，支持缓存和重新验证
  const response = await fetch('https://api.example.com/data', {
    headers: {
      'Authorization': `Bearer ${cookies().get('token')?.value}`
    },
    next: { revalidate: 60 } // 60秒内复用缓存数据
  });
  
  const data = await response.json();
  
  return <div>{/* 渲染数据 */}</div>;
}
```

### API 定义最佳实践

在实际项目中，应将API请求方法集中管理：

```typescript
// services/api/userRequestApi.ts
import { request } from '@/lib/request';
import { ApiResponse } from '@/types/api';
import { User } from '@/types/models';

// 获取用户列表
export const getUsersRequest = async (): Promise<User[]> => {
  try {
    const response = await request.get<ApiResponse<User[]>>('/api/users');
    return response.data || [];
  } catch (error) {
    console.error('获取用户列表失败:', error);
    return [];
  }
};

// 创建用户
export const createUserRequest = async (userData: Partial<User>): Promise<User | null> => {
  try {
    const response = await request.post<ApiResponse<User>>('/api/users', userData);
    return response.data || null;
  } catch (error) {
    console.error('创建用户失败:', error);
    return null;
  }
};

// 更新用户
export const updateUserRequest = async (id: string, userData: Partial<User>): Promise<boolean> => {
  try {
    const response = await request.put<ApiResponse<boolean>>(`/api/users/${id}`, userData);
    return !!response.data;
  } catch (error) {
    console.error('更新用户失败:', error);
    return false;
  }
};

// 删除用户
export const deleteUserRequest = async (id: string): Promise<boolean> => {
  try {
    const response = await request.delete<ApiResponse<boolean>>(`/api/users/${id}`);
    return !!response.data;
  } catch (error) {
    console.error('删除用户失败:', error);
    return false;
  }
};
```

### 直接使用request函数

除了使用HTTP方法（get、post等）外，还可以直接调用request函数：

```typescript
// 方式一：提供完整配置参数
const response1 = await request('/api/users', {
  method: 'get',
  params: { role: 'admin' }
});

// 方式二：提供数据和方法配置
const response2 = await request('/api/users', { id: 1 }, { method: 'get' });

// POST请求示例
const newUser = await request('/api/users', {
  name: '张三',
  email: '<EMAIL>'
}, {
  method: 'post'
});

// 复杂配置示例
const response = await request('/api/data', null, {
  method: 'get',
  params: { id: 1 },
  timeout: 5000,
  headers: {
    'X-Custom-Header': 'custom-value'
  },
  showLoading: true,
  loadingText: '加载中...'
});
```

### 带状态管理的请求

```typescript
// 显示加载状态
const getDataWithLoading = async () => {
  const result = await request.get('/api/data', {
    showLoading: true,
    loadingText: '加载数据中...'
  });
  return result;
};

// 显示成功消息
const createDataWithSuccess = async (data: any) => {
  const result = await request.post('/api/data', data, {
    showSuccessMessage: true,
    successMessage: '创建成功！'
  });
  return result;
};

// 自定义错误消息
const fetchDataWithErrorHandling = async () => {
  try {
    const result = await request.get('/api/data', {
      showErrorMessage: true,
      errorMessage: '获取数据失败，请稍后重试'
    });
    return result;
  } catch (error) {
    // 错误已被自动处理，无需额外处理
    throw error;
  }
};
```

### 创建自定义请求实例

```typescript
import { extend } from '@/lib/request';

const apiClient = extend({
  baseURL: 'https://api.example.com',
  timeout: 5000,
  headers: {
    'Content-Type': 'application/json'
  },
  requestStatusConfig: {
    defaultShowLoading: true,      // 默认显示加载状态
    defaultShowSuccessMessage: false,
    defaultShowErrorMessage: true
  }
});

export default apiClient;
```

### 添加认证拦截器

```typescript
import { request } from '@/lib/request';
import { setupAuthInterceptors } from '@/services/api/authInterceptor';

// 设置全局拦截器
setupAuthInterceptors(request);

// 然后就可以使用request发起请求了
const data = await request.get('/api/protected-resource');
```

### 请求取消

```typescript
import { request, CancelToken, isCancel } from '@/lib/request';

const fetchData = async () => {
  const source = CancelToken.source();
  
  try {
    const result = await request.get('/api/data', {
      cancelToken: source.token
    });
    return result;
  } catch (error) {
    if (isCancel(error)) {
      console.log('请求已取消', error.message);
      return { canceled: true };
    }
    throw error;
  }
  
  // 在需要时取消请求
  // source.cancel('用户取消了请求');
};
```

## 配置选项

请求配置支持所有 axios 的配置项，并额外增加了以下配置：

- `skipErrorHandler`: 是否跳过错误处理
- `getResponse`: 是否获取原始响应对象
- `requestInterceptors`: 请求拦截器
- `responseInterceptors`: 响应拦截器
- `showLoading`: 是否显示加载状态
- `loadingText`: 加载提示文本
- `showSuccessMessage`: 是否显示成功提示
- `successMessage`: 成功提示文本
- `showErrorMessage`: 是否显示错误提示
- `errorMessage`: 错误提示文本

## 拦截器

可以添加全局拦截器或者针对特定请求实例的拦截器：

```typescript
import { request } from '@/lib/request';

// 添加请求拦截器
request.interceptors.request.use((config) => {
  // 在请求发送前做些什么
  config.headers = {
    ...config.headers,
    'Authorization': `Bearer ${getToken()}`
  };
  return config;
});

// 添加响应拦截器
request.interceptors.response.use((response) => {
  // 对响应数据做些什么
  return response;
}, (error) => {
  // 对响应错误做些什么
  return Promise.reject(error);
});
```

## 类型安全

库提供了完整的类型支持，可以利用TypeScript泛型指定响应类型：

```typescript
interface User {
  id: number;
  name: string;
  email: string;
}

// 指定返回类型为User
const getUser = async (id: number): Promise<User> => {
  return request.get<User>('/api/users', { id });
};

interface CreateUserRequest {
  name: string;
  email: string;
}

// 指定请求和返回类型
const createUser = async (userData: CreateUserRequest): Promise<User> => {
  return request.post<User>('/api/users', userData);
};
```

### API响应处理工具

本库还提供了一系列API响应处理工具，帮助更好地处理响应数据和异常情况。这些工具位于 `lib/request/response-handler.ts` 文件中，并通过 `lib/request/index.ts` 导出。

#### 基本用法

```typescript
import { 
  processResponse, 
  processArrayResponse,
  handleApiError, 
  safeExecute 
} from '@/lib/request';
import { ApiResponse } from '@/types/api';
import { User } from '@/types/models';

// 处理API响应，获取数据或返回null
export const getUserRequest = async (id: string): Promise<User | null> => {
  try {
    const response = await request.get<ApiResponse<User>>(`/api/users/${id}`);
    
    // 处理响应，自动处理toast提示
    return processResponse(response, {
      successMessage: "获取用户信息成功", // 成功提示消息
      errorMessage: "获取用户信息失败",   // 错误提示消息
      showSuccessToast: false,          // 是否显示成功toast
      showErrorToast: true              // 是否显示错误toast
    });
  } catch (error) {
    // 处理异常，显示toast提示
    handleApiError(error, "获取用户信息失败", true);
    return null;
  }
};

// 处理返回数组的API响应
export const getUsersRequest = async (): Promise<User[]> => {
  try {
    const response = await request.get<ApiResponse<User[]>>('/api/users');
    
    // 处理数组响应，确保返回数组，即使API出错也返回空数组
    return processArrayResponse(response, {
      errorMessage: "获取用户列表失败",
      showErrorToast: true,
      // 自定义成功状态码检查
      customCodeCheck: (code) => {
        // 同时支持多种成功状态码格式
        return code === "200" || code === 200 || code === "0" || code === 0;
      }
    });
  } catch (error) {
    handleApiError(error, "获取用户列表失败");
    return [];
  }
};

// 使用安全执行包装器简化异步操作和错误处理
export const createUserRequest = async (userData: Partial<User>): Promise<User | null> => {
  // safeExecute自动处理try-catch和toast提示
  return safeExecute(
    // 异步操作函数
    async () => {
      const response = await request.post<ApiResponse<User>>('/api/users', userData);
      
      // 处理响应
      const user = processResponse(response, {
        // 响应处理选项会被safeExecute的选项覆盖
        showSuccessToast: false,
        showErrorToast: false
      });
      
      return user;
    },
    // 默认错误消息
    "创建用户失败",
    // 选项
    {
      showSuccessToast: true,
      successMessage: "用户创建成功",
      showErrorToast: true,
      // 成功回调
      onSuccess: (user) => {
        console.log("创建的用户ID:", user.id);
      },
      // 错误回调
      onError: (error) => {
        console.error("用户创建错误:", error);
      },
      // 请求失败自动重试次数
      retryCount: 1
    }
  );
};
```

#### API响应处理工具函数说明

##### processResponse

处理API响应数据，根据状态码判断请求是否成功，并提供toast提示。

```typescript
const result = processResponse(response, {
  successMessage: "操作成功",      // 成功提示消息
  errorMessage: "请求异常",        // 错误提示消息
  showSuccessToast: false,        // 是否显示成功提示
  showErrorToast: true,           // 是否显示错误提示
  customCodeCheck: (code) => boolean // 自定义状态码检查函数
});
```

##### processArrayResponse

处理返回数组的API响应，确保即使API出错也返回空数组而不是null。

```typescript
const items = processArrayResponse(response, options);
// 即使response为null或undefined，items始终是数组(可能为空)
```

##### handleApiError

统一处理API异常，并提供友好的错误信息。

```typescript
try {
  // API请求代码
} catch (error) {
  const { message } = handleApiError(
    error,                // 捕获的异常
    "操作失败",           // 默认错误消息
    true                  // 是否显示Toast提示
  );
  // 可以进一步处理错误
}
```

##### safeExecute

安全执行异步操作的包装函数，简化try-catch和错误处理。

```typescript
const result = await safeExecute(
  async () => {
    // 异步操作代码
    return data;
  },
  "操作执行失败",  // 默认错误消息
  {
    showSuccessToast: false,        // 是否显示成功提示
    successMessage: "操作成功",      // 成功提示消息
    showErrorToast: true,           // 是否显示错误提示
    onSuccess: (result) => void,    // 成功回调
    onError: (error) => void,       // 错误回调
    retryCount: 0                   // 失败重试次数
  }
);
``` 