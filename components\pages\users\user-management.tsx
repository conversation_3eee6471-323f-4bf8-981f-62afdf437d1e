"use client"

import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { 
  Edit, 
  Trash2, 
  Eye,
  UserPlus
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "sonner"
import { formatDate } from "@/lib/utils"
import { 
  Alert, 
  AlertDescription, 
  AlertTitle 
} from "@/components/ui/alert"
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useRouter } from "next/navigation"
import { DataTable } from "@/components/custom/data-table/data-table"
import FormDialog, { FormField } from "@/components/custom/form-dialog"
import { type ColumnDef } from "@tanstack/react-table"

// 导入用户相关API
import { 
  getUserPageRequest, 
  createUserRequest, 
  deleteUserRequest,
  UserCreateParams
} from "@/services/api/userRequestApi"
import { User as UserType, UserQueryParams } from "@/types/user"

export default function UserManagement() {
  // 状态定义
  const [users, setUsers] = useState<UserType[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // 分页状态
  const [pagination, setPagination] = useState<{ total: number; pageNum: number; pageSize: number; pages: number }>({ total: 0, pageNum: 1, pageSize: 10, pages: 1 })
  
  // 对话框状态
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [deleteConfirmName, setDeleteConfirmName] = useState("")
  const [userToDelete, setUserToDelete] = useState<UserType | null>(null)
  
  const router = useRouter()
  
  // 加载用户数据
  const loadUsers = async (searchAccount?: string) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const params: UserQueryParams = {
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize,
        account: searchAccount || undefined,
        status: undefined
      }
      
      const result = await getUserPageRequest(params)
      setUsers(result.list || [])
      setPagination({
        total: result.total || 0,
        pageNum: result.pageNum || 1,
        pageSize: result.pageSize || 10,
        pages: result.pages || 1
      })
    } catch (err) {
      console.error("加载用户数据失败", err)
      setError("加载用户数据失败，请稍后重试")
    } finally {
      setIsLoading(false)
    }
  }
  
  // 初始加载
  useEffect(() => {
    loadUsers()
  }, [pagination.pageNum, pagination.pageSize])
  
  // 处理创建用户
  const handleCreateUser = async (values: Record<string, any>) => {
    // 邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(values.email)) {
      toast.error("邮箱格式不正确")
      throw new Error("邮箱格式不正确")
    }
    
    try {
      // 适配API接口需要的格式
      const params: UserCreateParams = {
        username: values.account.trim(), // API需要username
        password: "123456", // 默认密码
        realName: values.nickname.trim() || values.account.trim(), // API需要realName
        email: values.email.trim(),
        phone: values.mobile?.trim() || undefined // API需要phone
      }
      
      const result = await createUserRequest(params)
      
      if (result) {
        toast.success("用户创建成功")
        // 重新加载用户列表
        await loadUsers()
      } else {
        toast.error("用户创建失败，请重试")
        throw new Error("用户创建失败")
      }
    } catch (err) {
      console.error("创建用户失败", err)
      toast.error("创建用户失败，请重试")
      throw err
    }
  }
  
  // 处理删除用户
  const handleDeleteUser = async () => {
    if (!userToDelete || !userToDelete.id) return
    
    try {
      await deleteUserRequest(userToDelete.id)
      toast.success("用户删除成功")
      setIsDeleteDialogOpen(false)
      setUserToDelete(null)
      setDeleteConfirmName("")
      
      // 重新加载用户列表
      await loadUsers()
    } catch (err) {
      console.error("删除用户失败", err)
      toast.error("删除用户失败，请重试")
    }
  }
  
  // 打开删除确认对话框
  const openDeleteDialog = (user: UserType) => {
    setUserToDelete(user)
    setDeleteConfirmName("")
    setIsDeleteDialogOpen(true)
  }
  
  // 处理用户操作
  const handleUserAction = (user: UserType, action: string) => {
    switch (action) {
      case "view":
        router.push(`/user/detail/${user.id}`)
        break
      case "edit":
        router.push(`/user/detail/${user.id}/edit`)
        break
      case "delete":
        openDeleteDialog(user)
        break
      default:
        break
    }
  }
  
  // 处理搜索
  const handleSearch = (searchTerm: string) => {
    // 重置到第一页并使用搜索词加载数据
    setPagination(prev => ({
      ...prev,
      pageNum: 1
    }))
    loadUsers(searchTerm)
  }
  
  // 处理刷新
  const handleRefresh = () => {
    loadUsers()
  }
  
  // 获取状态徽章样式
  const getStatusBadgeStyle = (status: number) => {
    switch (status) {
      case 1:
        return "bg-green-100 text-green-800 border-green-200"
      case 0:
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case 2:
        return "bg-red-100 text-red-800 border-red-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }
  
  // 获取状态标签
  const getStatusLabel = (status?: number) => {
    switch (status) {
      case 1:
        return "正常"
      case 0:
        return "待激活"
      case 2:
        return "已禁用"
      default:
        return "未知"
    }
  }

  // 用户表单字段定义
  const userFormFields: FormField[] = [
    {
      id: "account",
      label: "账号",
      type: "text",
      placeholder: "请输入用户账号",
      required: true
    },
    {
      id: "email",
      label: "邮箱",
      type: "email",
      placeholder: "请输入用户邮箱",
      required: true,
      validator: (value) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        return emailRegex.test(value) ? null : "邮箱格式不正确"
      }
    },
    {
      id: "nickname",
      label: "姓名",
      type: "text",
      placeholder: "请输入用户姓名（选填）"
    },
    {
      id: "mobile",
      label: "手机号",
      type: "text",
      placeholder: "请输入手机号码（选填）"
    }
  ]

  // 定义表格列
  const columns: ColumnDef<UserType>[] = [
    {
      accessorKey: "account",
      header: "账号",
      cell: ({ row }) => <div className="font-medium">{row.original.account}</div>
    },
    {
      accessorKey: "nickname",
      header: "姓名",
      cell: ({ row }) => <div>{row.original.nickname || "-"}</div>
    },
    {
      accessorKey: "email",
      header: "邮箱",
      cell: ({ row }) => <div>{row.original.email || "-"}</div>
    },
    {
      accessorKey: "mobile",
      header: "手机号",
      cell: ({ row }) => <div>{row.original.mobile || "-"}</div>
    },
    {
      accessorKey: "status",
      header: "状态",
      cell: ({ row }) => (
        <Badge className={getStatusBadgeStyle(row.original.status || 0)}>
          {getStatusLabel(row.original.status)}
        </Badge>
      )
    },
    {
      accessorKey: "createTime",
      header: "创建时间",
      cell: ({ row }) => <div>{row.original.createTime ? formatDate(row.original.createTime) : "-"}</div>
    }
  ]

  return (
    <div className="space-y-4 p-8">
      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTitle>错误</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">用户管理</h2>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <UserPlus className="mr-2 h-4 w-4" />
          创建用户
        </Button>
      </div>

      <DataTable
        columns={columns}
        data={users}
        config={{
          showSelection: false,
          showSearch: true,
          showColumnVisibility: true,
          showPagination: true,
          pageSize: pagination.pageSize,
          pageSizeOptions: [10, 20, 30, 50],
          searchPlaceholder: "搜索用户账号..."
        }}
        searchKey="account"
        loading={isLoading}
        primaryActions={[
          {
            label: "查看",
            value: "view",
            icon: Eye,
            variant: "outline"
          },
          {
            label: "编辑",
            value: "edit",
            icon: Edit,
            variant: "outline"
          },
          {
            label: "删除",
            value: "delete",
            icon: Trash2,
            variant: "outline"
          }
        ]}
        onRowAction={handleUserAction}
        filters={[]}
        onRefresh={handleRefresh}
      />
      
      {/* 创建用户对话框 */}
      <FormDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        title="创建用户"
        description="创建新用户账号。系统将向用户发送激活邮件。"
        fields={userFormFields}
        onSubmit={handleCreateUser}
        submitButtonText="创建"
        maxWidth="sm"
      />
      
      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除用户</AlertDialogTitle>
            <AlertDialogDescription>
              此操作将永久删除用户 <strong>{userToDelete?.nickname || userToDelete?.account}</strong>。删除后不可恢复。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="py-4">
            <Label htmlFor="confirmName">请输入用户账号 <strong>{userToDelete?.account}</strong> 以确认删除：</Label>
            <Input
              id="confirmName"
              placeholder={`输入 ${userToDelete?.account} 确认`}
              value={deleteConfirmName}
              onChange={e => setDeleteConfirmName(e.target.value)}
              className="mt-2"
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteUser}
              disabled={deleteConfirmName !== userToDelete?.account}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
} 