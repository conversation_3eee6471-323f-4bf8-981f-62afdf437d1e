"use client"

import * as React from "react"
import {
    type ColumnDef,
    type ColumnFiltersState,
    type SortingState,
    type VisibilityState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table"
import { Filter, MoreHorizontal, Search } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { cn } from "@/lib/utils"
import type { DataTableProps, TableConfig, TableFilter } from "./types"
import { Pagination } from "@/components/custom/pagination"
import { DataTableToolbar } from "./data-table-toolbar"

const defaultConfig: TableConfig = {
    showSelection: true,
    showPagination: true,
    showSearch: true,
    showFilters: true,
    showColumnVisibility: true,
    showRowActions: true,
    pageSize: 10,
    pageSizeOptions: [10, 20, 30, 40, 50],
    searchPlaceholder: "搜索...",
    emptyMessage: "暂无数据",
    loadingMessage: "加载中...",
}

export function DataTable<TData, TValue>({
                                             columns,
                                             data,
                                             config = {},
                                             searchKey,
                                             filters = [],
                                             primaryActions = [],
                                             secondaryActions = [],
                                             onRowAction,
                                             loading = false,
                                             className,
                                             onRefresh,
                                         }: DataTableProps<TData, TValue>) {
    const tableConfig = { ...defaultConfig, ...config }
    const [density, setDensity] = React.useState<"compact" | "default" | "comfortable">("default")
    const [sorting, setSorting] = React.useState<SortingState>([])
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
    const [rowSelection, setRowSelection] = React.useState({})
    const [searchValue, setSearchValue] = React.useState("")

    // 当searchValue变化时，更新列过滤器
    React.useEffect(() => {
        if (searchKey) {
            setColumnFilters(prev => {
                const newFilters = prev.filter(filter => filter.id !== searchKey);
                if (searchValue) {
                    newFilters.push({
                        id: searchKey,
                        value: searchValue,
                    });
                }
                return newFilters;
            });
        }
    }, [searchValue, searchKey]);

    // 构建列定义
    const columnsWithExtras = React.useMemo(() => {
        let newColumns = [...columns]

        // 添加选择列
        if (tableConfig.showSelection) {
            const selectionColumn: ColumnDef<TData, TValue> = {
                id: "select",
                header: ({ table }) => (
                    <div className="pl-4">
                        <Checkbox
                            checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
                            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                            aria-label="全选"
                        />
                    </div>
                ),
                cell: ({ row }) => (
                    <div className="pl-4">
                        <Checkbox
                            checked={row.getIsSelected()}
                            onCheckedChange={(value) => row.toggleSelected(!!value)}
                            aria-label="选择行"
                        />
                    </div>
                ),
                enableSorting: false,
                enableHiding: false,
            }
            newColumns = [selectionColumn, ...newColumns]
        }

        // 为其他所有列添加左对齐
        newColumns = newColumns.map(column => {
            if (column.id !== "select" && column.id !== "actions") {
                return {
                    ...column,
                    header: typeof column.header === 'function' 
                        ? column.header 
                        : () => <div className="text-left">{column.header as React.ReactNode}</div>,
                }
            }
            return column;
        });

        // 添加操作列
        if (tableConfig.showRowActions && (primaryActions.length > 0 || secondaryActions.length > 0)) {
            const actionsColumn: ColumnDef<TData, TValue> = {
                id: "actions",
                header: () => <div className="text-left pr-4">操作</div>,
                cell: ({ row }) => {
                    const visibleActions = primaryActions.slice(0, 2)
                    const moreActions = [...primaryActions.slice(2), ...secondaryActions]

                    return (
                        <div className="flex items-center justify-end gap-2 pr-4">
                            {visibleActions.map((action) => (
                                <Button
                                    key={action.value}
                                    variant="ghost"
                                    size="icon"
                                    className="cursor-pointer"
                                    onClick={() => {
                                        action.onClick?.(row.original)
                                        onRowAction?.(row.original, action.value)
                                    }}
                                    title={action.label}
                                >
                                    {action.icon && <action.icon className="h-4 w-4" />}
                                </Button>
                            ))}

                            {moreActions.length > 0 && (
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" size="icon" className="cursor-pointer">
                                            <MoreHorizontal className="h-4 w-4" />
                                            <span className="sr-only">更多操作</span>
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end" className="min-w-[120px] p-1">
                                        {moreActions.map((action) => (
                                            <DropdownMenuItem
                                                key={action.value}
                                                onClick={() => {
                                                    action.onClick?.(row.original)
                                                    onRowAction?.(row.original, action.value)
                                                }}
                                                className="text-foreground text-sm py-1.5 cursor-pointer"
                                            >
                                                {action.icon && <action.icon className="mr-2 h-3.5 w-3.5" />}
                                                {action.label}
                                            </DropdownMenuItem>
                                        ))}
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            )}
                        </div>
                    )
                },
                enableSorting: false,
                enableHiding: false,
                size: primaryActions.length > 0 ? (primaryActions.length > 1 ? 100 : 60) : 60,
            }
            newColumns = [...newColumns, actionsColumn]
        }

        return newColumns
    }, [columns, tableConfig.showSelection, tableConfig.showRowActions, primaryActions, secondaryActions, onRowAction])

    const table = useReactTable({
        data,
        columns: columnsWithExtras,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        initialState: {
            pagination: {
                pageSize: tableConfig.pageSize || 10,
            },
        },
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            rowSelection,
        },
    })

    // 渲染筛选器
    const renderFilter = (filter: TableFilter) => {
        const column = table.getColumn(filter.key)
        if (!column) return null

        switch (filter.type) {
            case "search":
                return (
                    <div key={filter.key} className="relative">
                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                            placeholder={filter.placeholder || `搜索${filter.title}...`}
                            value={(column.getFilterValue() as string) ?? ""}
                            onChange={(event) => column.setFilterValue(event.target.value)}
                            className="pl-8 w-[200px]"
                        />
                    </div>
                )

            case "multi-select":
                return (
                    <DropdownMenu key={filter.key}>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm" className="border-dashed cursor-pointer">
                                <Filter className="mr-2 h-4 w-4" />
                                {filter.title}
                                {column.getFilterValue() && (
                                    <Badge variant="secondary" className="ml-2 h-4 w-4 p-0 text-xs">
                                        {Array.isArray(column.getFilterValue()) ? ((column.getFilterValue() as string[]).length as React.ReactNode) : (1 as React.ReactNode)}
                                    </Badge>
                                )}
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="start" className="w-[200px]">
                            <DropdownMenuLabel>{filter.title}</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            {filter.options?.map((option) => {
                                const isSelected = Array.isArray(column.getFilterValue())
                                    ? (column.getFilterValue() as string[]).includes(option.value)
                                    : column.getFilterValue() === option.value

                                const Icon = option.icon;
                                
                                return (
                                    <DropdownMenuCheckboxItem
                                        key={option.value}
                                        checked={isSelected}
                                        onCheckedChange={(checked) => {
                                            const currentValue = (column.getFilterValue() as string[]) || []
                                            if (checked) {
                                                column.setFilterValue([...currentValue, option.value])
                                            } else {
                                                column.setFilterValue(currentValue.filter((v) => v !== option.value))
                                            }
                                        }}
                                    >
                                        {Icon && <Icon className="mr-2 h-4 w-4" />}
                                        {option.label}
                                    </DropdownMenuCheckboxItem>
                                )
                            })}
                        </DropdownMenuContent>
                    </DropdownMenu>
                )

            default:
            case "select":
                return (
                    <DropdownMenu key={filter.key}>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm" className="border-dashed cursor-pointer">
                                <Filter className="mr-2 h-4 w-4" />
                                {filter.title}
                                {column.getFilterValue() && (
                                    <Badge variant="secondary" className="ml-2 h-4 w-4 p-0 text-xs">
                                        {1 as React.ReactNode}
                                    </Badge>
                                )}
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="start">
                            <DropdownMenuLabel>{filter.title}</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => column.setFilterValue("")}>全部</DropdownMenuItem>
                            {filter.options?.map((option) => {
                                const Icon = option.icon;
                                
                                return (
                                    <DropdownMenuItem key={option.value} onClick={() => column.setFilterValue(option.value)}>
                                        {Icon && <Icon className="mr-2 h-4 w-4" />}
                                        {option.label}
                                    </DropdownMenuItem>
                                )
                            })}
                        </DropdownMenuContent>
                    </DropdownMenu>
                )
        }
    }

    // 获取表格行高度
    const getTableRowHeight = () => {
        switch (density) {
            case "compact":
                return "h-8";
            case "comfortable":
                return "h-12";
            default:
                return "h-10";
        }
    };

    // 获取表格单元格内边距
    const getTableCellPadding = () => {
        switch (density) {
            case "compact":
                return "py-1";
            case "comfortable":
                return "py-3";
            default:
                return "py-2";
        }
    };

    return (
        <div className={cn("w-full", className)}>
            {/* 工具栏 */}
            {(tableConfig.showSearch || tableConfig.showFilters || tableConfig.showColumnVisibility) && (
                <DataTableToolbar
                    searchKey={searchKey || ""}
                    searchValue={searchValue}
                    onSearchChange={setSearchValue}
                    filters={filters}
                    renderFilter={renderFilter}
                    columns={columns}
                    columnVisibility={columnVisibility}
                    onColumnVisibilityChange={setColumnVisibility}
                    density={density}
                    onDensityChange={setDensity}
                    onRefresh={onRefresh || (() => {
                        // 默认刷新逻辑
                        console.log("刷新表格数据");
                    })}
                />
            )}

            {/* 表格 */}
            <div className="rounded-md border overflow-hidden">
                <div className="relative w-full overflow-auto">
                    <Table>
                        <TableHeader>
                            {table.getHeaderGroups().map((headerGroup) => (
                                <TableRow key={headerGroup.id} className="bg-muted/50">
                                    {headerGroup.headers.map((header) => {
                                        return (
                                            <TableHead 
                                                key={header.id}
                                                style={{ width: header.getSize() }}
                                                className={getTableCellPadding()}
                                            >
                                                {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                                            </TableHead>
                                        )
                                    })}
                                </TableRow>
                            ))}
                        </TableHeader>
                        <TableBody>
                            {loading ? (
                                Array.from({ length: tableConfig.pageSize || 10 }).map((_, index) => (
                                    <TableRow key={index} className={getTableRowHeight()}>
                                        {columnsWithExtras.map((_, cellIndex) => (
                                            <TableCell key={cellIndex} className={getTableCellPadding()}>
                                                <Skeleton className="h-4 w-full" />
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : table.getRowModel().rows?.length ? (
                                table.getRowModel().rows.map((row) => (
                                    <TableRow 
                                        key={row.id} 
                                        data-state={row.getIsSelected() && "selected"}
                                        className={getTableRowHeight()}
                                    >
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell key={cell.id} className={getTableCellPadding()}>
                                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={columnsWithExtras.length} className="h-24 text-center">
                                        {tableConfig.emptyMessage}
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>
            </div>

            {/* 分页 */}
            {tableConfig.showPagination && (
                <Pagination
                    pageIndex={table.getState().pagination.pageIndex}
                    pageCount={table.getPageCount()}
                    pageSize={table.getState().pagination.pageSize}
                    pageSizeOptions={tableConfig.pageSizeOptions}
                    totalItems={table.getFilteredRowModel().rows.length}
                    onPageChange={(page) => table.setPageIndex(page)}
                    onPageSizeChange={(size) => table.setPageSize(size)}
                />
            )}
        </div>
    )
}
