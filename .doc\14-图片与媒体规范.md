# 图片与媒体规范

图片和媒体资源是用户界面的重要组成部分，影响着应用的性能、视觉体验和可访问性。本文档规范了项目中图片和媒体资源的格式、优化方法和使用原则。

## 图片格式选择

### 不同图片格式的应用场景

| 格式 | 最佳应用场景 | 特点 |
|------|------------|------|
| WebP | 通用网页图片 | 同时支持有损和无损压缩，比JPEG小约25-35%，比PNG小约25-40% |
| AVIF | 高质量图像 | 更高的压缩率，比WebP再减少约20%的体积 |
| JPEG/JPG | 照片、渐变图像 | 有损压缩，不支持透明度 |
| PNG | 需要透明度的图像、图标 | 无损压缩，支持透明度，文件较大 |
| SVG | 图标、简单图形、动画 | 矢量格式，缩放不失真，适合简单图形 |
| GIF | 简单动画 | 支持动画，但色彩有限，文件通常较大 |

```jsx
// 根据内容选择合适的图片格式
// 1. 复杂照片类图像
<Image 
  src="/images/photo.webp" // 首选WebP
  alt="风景照片"
  width={800}
  height={600}
/>

// 2. 需要透明度的图像
<Image 
  src="/images/logo.png" // 使用PNG支持透明度
  alt="公司Logo"
  width={200}
  height={80}
/>

// 3. 图标和简单图形
<svg 
  width="24" 
  height="24" 
  viewBox="0 0 24 24" 
  fill="none" 
  xmlns="http://www.w3.org/2000/svg"
>
  <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" strokeWidth="2"/>
</svg>
```

## 图片优化策略

### 响应式图片

根据不同设备和屏幕尺寸提供不同分辨率的图片，优化加载性能和显示效果。

```jsx
// 使用Next.js的Image组件实现响应式图片
import Image from "next/image"

function ResponsiveImage() {
  return (
    <Image
      src="/images/hero.webp"
      alt="Hero Image"
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      priority
      quality={85}
      fill
      className="object-cover"
    />
  )
}

// 使用srcSet属性提供多个图片尺寸
<img
  src="/images/product-sm.webp"
  srcSet="/images/product-sm.webp 400w, /images/product-md.webp 800w, /images/product-lg.webp 1200w"
  sizes="(max-width: 600px) 400px, (max-width: 1200px) 800px, 1200px"
  alt="Product Image"
  loading="lazy"
/>
```

### 懒加载

只在图片接近或进入视口时才加载图片，减少初始页面加载时间。

```jsx
// 使用Next.js的Image组件自动实现懒加载
<Image
  src="/images/content.webp"
  alt="Content Image"
  width={600}
  height={400}
  // 不设置priority属性，默认懒加载
/>

// 使用原生的loading="lazy"属性
<img
  src="/images/content.jpg"
  alt="Content Image"
  width={600}
  height={400}
  loading="lazy"
/>
```

### 预加载关键图像

对于首屏重要图像，应当预加载以提升用户体验。

```jsx
// 使用Next.js的Image组件预加载关键图像
<Image
  src="/images/hero-banner.webp"
  alt="Hero Banner"
  width={1200}
  height={600}
  priority // 指示预加载
/>

// 使用link标签预加载
// 在页面的head部分添加
<Head>
  <link
    rel="preload"
    href="/images/hero-banner.webp"
    as="image"
  />
</Head>
```

### 图片压缩与优化

所有图片应当经过适当压缩，减少文件大小同时保持足够的视觉质量。

```jsx
// 使用Next.js的Image组件控制图片质量
<Image
  src="/images/photo.webp"
  alt="Photo"
  width={800}
  height={600}
  quality={80} // 设置压缩质量(0-100)
/>
```

推荐的图片优化工具：
1. [Sharp](https://sharp.pixelplumbing.com/) - Node.js图像处理库
2. [Squoosh](https://squoosh.app/) - 在线图像压缩工具
3. [TinyPNG](https://tinypng.com/) - PNG和JPEG压缩服务
4. [SVGO](https://github.com/svg/svgo) - SVG优化工具

## 图片组件与布局

### 基础图片组件

创建一个标准的图片组件，统一处理图片的加载、错误处理和样式。

```jsx
// components/custom/base-image.tsx
import { useState } from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"

interface BaseImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  fill?: boolean
  priority?: boolean
  quality?: number
  className?: string
  fallbackSrc?: string
  aspectRatio?: "square" | "video" | "portrait" | "custom"
  objectFit?: "contain" | "cover" | "fill" | "none" | "scale-down"
}

export function BaseImage({
  src,
  alt,
  width,
  height,
  fill = false,
  priority = false,
  quality = 80,
  className = "",
  fallbackSrc = "/images/image-placeholder.svg",
  aspectRatio = "custom",
  objectFit = "cover",
}: BaseImageProps) {
  const [imgSrc, setImgSrc] = useState(src)
  
  // 处理图片加载错误
  const handleError = () => {
    setImgSrc(fallbackSrc)
  }
  
  // 根据宽高比设置类名
  const aspectRatioClass = {
    square: "aspect-square",
    video: "aspect-video",
    portrait: "aspect-[3/4]",
    custom: "",
  }[aspectRatio]
  
  return (
    <div className={cn(
      "overflow-hidden",
      aspectRatioClass,
      className
    )}>
      <Image
        src={imgSrc}
        alt={alt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        priority={priority}
        quality={quality}
        onError={handleError}
        className={cn(
          "transition-opacity duration-300",
          `object-${objectFit}`
        )}
      />
    </div>
  )
}
```

### 图片容器布局

常见的图片容器布局方式及其实现。

```jsx
// 1. 卡片中的图片
<Card>
  <div className="relative aspect-video overflow-hidden rounded-t-lg">
    <BaseImage
      src="/images/card-image.webp"
      alt="Card Image"
      fill
      priority={false}
    />
  </div>
  <CardContent>
    <h3 className="text-lg font-medium">卡片标题</h3>
    <p className="text-muted-foreground">卡片描述内容</p>
  </CardContent>
</Card>

// 2. 全宽背景图片
<div className="relative h-[50vh] w-full">
  <BaseImage
    src="/images/hero-background.webp"
    alt="Background Image"
    fill
    priority
    className="after:absolute after:inset-0 after:bg-gradient-to-b after:from-transparent after:to-black/60 after:content-['']"
  />
  <div className="relative z-10 flex h-full items-center justify-center">
    <h1 className="text-4xl font-bold text-white">页面标题</h1>
  </div>
</div>

// 3. 图片网格布局
<div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
  {images.map((image) => (
    <div key={image.id} className="overflow-hidden rounded-lg">
      <BaseImage
        src={image.src}
        alt={image.alt}
        aspectRatio="square"
        className="transition-transform duration-300 hover:scale-105"
      />
    </div>
  ))}
</div>
```

### 图片加载态与错误态

处理图片在加载和错误状态下的显示效果。

```jsx
// components/custom/image-with-loading.tsx
import { useState } from "react"
import Image from "next/image"
import { Skeleton } from "@/components/ui/skeleton"
import { ImageOff } from "lucide-react"

interface ImageWithLoadingProps {
  src: string
  alt: string
  width: number
  height: number
  className?: string
}

export function ImageWithLoading({
  src,
  alt,
  width,
  height,
  className = "",
}: ImageWithLoadingProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  
  return (
    <div 
      className={`relative overflow-hidden ${className}`}
      style={{ width, height }}
    >
      {isLoading && !hasError && (
        <Skeleton className="absolute inset-0" />
      )}
      
      {hasError ? (
        <div className="flex h-full w-full items-center justify-center bg-muted">
          <ImageOff className="h-6 w-6 text-muted-foreground" />
        </div>
      ) : (
        <Image
          src={src}
          alt={alt}
          width={width}
          height={height}
          className={`transition-opacity duration-300 ${isLoading ? 'opacity-0' : 'opacity-100'}`}
          onLoad={() => setIsLoading(false)}
          onError={() => {
            setIsLoading(false)
            setHasError(true)
          }}
        />
      )}
    </div>
  )
}
```

## 图片内容指南

### 图片风格一致性

项目中的图片应保持风格一致，包括色调、构图和处理方式。

```jsx
// 示例：保持一致的风格
<div className="grid grid-cols-1 gap-6 md:grid-cols-3">
  {/* 所有产品图片使用相同的背景、角度和光线 */}
  <BaseImage 
    src="/images/product-1.webp" 
    alt="产品1" 
    aspectRatio="square" 
  />
  <BaseImage 
    src="/images/product-2.webp" 
    alt="产品2" 
    aspectRatio="square" 
  />
  <BaseImage 
    src="/images/product-3.webp" 
    alt="产品3" 
    aspectRatio="square" 
  />
</div>
```

### 图片与文字的关系

在图文混合内容中，确保图片与文字的合理布局和视觉平衡。

```jsx
// 1. 左图右文布局
<div className="flex flex-col gap-6 md:flex-row">
  <div className="w-full md:w-1/2">
    <BaseImage
      src="/images/feature.webp"
      alt="Feature Image"
      aspectRatio="video"
    />
  </div>
  <div className="w-full md:w-1/2">
    <h2 className="text-2xl font-bold">功能标题</h2>
    <p className="mt-2 text-muted-foreground">
      详细描述文本内容，与图片保持适当的视觉平衡。文本长度应该与图片区域在视觉上保持平衡。
    </p>
  </div>
</div>

// 2. 文字叠加在图片上
<div className="relative aspect-video overflow-hidden rounded-lg">
  <BaseImage
    src="/images/banner.webp"
    alt="Banner Image"
    fill
    priority
  />
  <div className="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent">
    <div className="flex h-full flex-col justify-center p-6 text-white">
      <h2 className="text-3xl font-bold">叠加标题</h2>
      <p className="mt-2 max-w-md">
        确保文字在图片上有足够的对比度，可以使用渐变遮罩增强可读性。
      </p>
    </div>
  </div>
</div>
```

## 视频与动态媒体

### 嵌入视频

处理视频内容的嵌入和优化。

```jsx
// 1. 本地视频
<div className="relative aspect-video overflow-hidden rounded-lg">
  <video
    autoPlay
    muted
    loop
    playsInline
    poster="/images/video-poster.webp"
    className="h-full w-full object-cover"
  >
    <source src="/videos/background.webm" type="video/webm" />
    <source src="/videos/background.mp4" type="video/mp4" />
    您的浏览器不支持视频标签。
  </video>
</div>

// 2. YouTube视频嵌入
function YouTubeEmbed({ videoId, title }) {
  return (
    <div className="relative aspect-video overflow-hidden rounded-lg">
      <iframe
        src={`https://www.youtube.com/embed/${videoId}`}
        title={title}
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
        className="absolute inset-0 h-full w-full border-0"
      ></iframe>
    </div>
  )
}
```

### 优化视频加载

实现视频的延迟加载和预览图像。

```jsx
// components/custom/lazy-video.tsx
import { useState, useRef, useEffect } from "react"

interface LazyVideoProps {
  src: string
  posterSrc: string
  type?: string
  className?: string
  autoPlay?: boolean
  loop?: boolean
  muted?: boolean
}

export function LazyVideo({
  src,
  posterSrc,
  type = "video/mp4",
  className = "",
  autoPlay = true,
  loop = true,
  muted = true,
}: LazyVideoProps) {
  const [isInView, setIsInView] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)
  
  useEffect(() => {
    if (!videoRef.current) return
    
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      { threshold: 0.1 }
    )
    
    observer.observe(videoRef.current)
    
    return () => {
      if (videoRef.current) observer.disconnect()
    }
  }, [])
  
  return (
    <video
      ref={videoRef}
      poster={posterSrc}
      autoPlay={autoPlay && isInView}
      loop={loop}
      muted={muted}
      playsInline
      className={className}
    >
      {isInView && <source src={src} type={type} />}
      您的浏览器不支持视频标签。
    </video>
  )
}

// 使用示例
<LazyVideo
  src="/videos/product-demo.mp4"
  posterSrc="/images/video-thumbnail.webp"
  className="w-full rounded-lg"
/>
```

## 媒体内容可访问性

### 替代文本

所有图片必须提供有意义的替代文本，描述图片内容和功能。

```jsx
// 好的替代文本示例
<Image
  src="/images/product.webp"
  alt="红色皮革钱包，打开状态，展示了三个卡槽和一个钞票夹"
  width={500}
  height={300}
/>

// 装饰性图片
<Image
  src="/images/decorative-pattern.webp"
  alt="" // 装饰性图片使用空alt
  width={200}
  height={200}
  aria-hidden="true"
/>
```

### 字幕和描述

视频内容应提供字幕和文字描述，提高可访问性。

```jsx
// 视频带字幕
<video controls>
  <source src="/videos/tutorial.mp4" type="video/mp4" />
  <track
    kind="subtitles"
    src="/videos/subtitles-zh.vtt"
    srclang="zh"
    label="中文"
    default
  />
  <track
    kind="subtitles"
    src="/videos/subtitles-en.vtt"
    srclang="en"
    label="English"
  />
  您的浏览器不支持视频标签。
</video>

// 视频文字描述
<div>
  <video controls>
    <source src="/videos/product-demo.mp4" type="video/mp4" />
  </video>
  <details>
    <summary className="cursor-pointer text-sm font-medium">视频文字描述</summary>
    <div className="mt-2 text-sm text-muted-foreground">
      此视频展示了产品的主要功能。首先，演示了如何打开应用程序并登录。
      然后，展示了主要功能区的操作方法，包括数据分析页面的使用和报告生成流程。
      最后，视频展示了如何导出数据和分享报告。
    </div>
  </details>
</div>
```

## 图像管理与组织

### 图像目录结构

推荐的图像文件组织结构，确保资源管理清晰有序。

```
/public
  /images
    /ui          # 界面元素图像
      logo.svg
      icon-*.svg
    /backgrounds # 背景图像
      header-bg.webp
      pattern-*.webp
    /content     # 内容图像
      hero-*.webp
      feature-*.webp
    /products    # 产品图像
      product-*-sm.webp
      product-*-md.webp
      product-*-lg.webp
    /placeholders # 占位图像
      user-placeholder.svg
      image-placeholder.svg
  /videos
    background-video.mp4
    tutorial-*.mp4
```

### 图像命名约定

推荐的图像命名规范，提高可维护性。

```
[内容类型]-[具体描述]-[尺寸/变体].[格式]

例如:
- hero-homepage-lg.webp
- product-leather-wallet-red-sm.webp
- icon-arrow-right.svg
- background-pattern-light.webp
```

## 高级图像功能

### 图像比较滑块

实现前后对比的图像滑块组件。

```jsx
// components/custom/image-comparison.tsx
import { useState, useRef, useEffect } from "react"
import Image from "next/image"

interface ImageComparisonProps {
  beforeSrc: string
  afterSrc: string
  beforeAlt: string
  afterAlt: string
  width: number
  height: number
}

export function ImageComparison({
  beforeSrc,
  afterSrc,
  beforeAlt,
  afterAlt,
  width,
  height,
}: ImageComparisonProps) {
  const [sliderPosition, setSliderPosition] = useState(50)
  const containerRef = useRef<HTMLDivElement>(null)
  
  const handleMove = (e: React.MouseEvent | React.TouchEvent) => {
    if (!containerRef.current) return
    
    const rect = containerRef.current.getBoundingClientRect()
    const x = "touches" in e 
      ? e.touches[0].clientX - rect.left 
      : e.clientX - rect.left
    
    const position = Math.max(0, Math.min(100, (x / rect.width) * 100))
    setSliderPosition(position)
  }
  
  return (
    <div
      ref={containerRef}
      className="relative cursor-col-resize overflow-hidden rounded-lg"
      style={{ width, height }}
      onMouseMove={handleMove}
      onTouchMove={handleMove}
    >
      {/* Before Image (Full Width) */}
      <div className="absolute inset-0">
        <Image
          src={beforeSrc}
          alt={beforeAlt}
          fill
          className="object-cover"
        />
      </div>
      
      {/* After Image (Clipped) */}
      <div 
        className="absolute inset-0 overflow-hidden"
        style={{ width: `${sliderPosition}%` }}
      >
        <Image
          src={afterSrc}
          alt={afterAlt}
          fill
          className="object-cover"
        />
      </div>
      
      {/* Slider Handle */}
      <div 
        className="absolute bottom-0 top-0 z-10 w-1 cursor-col-resize bg-white"
        style={{ left: `${sliderPosition}%` }}
      >
        <div className="absolute left-1/2 top-1/2 h-8 w-8 -translate-x-1/2 -translate-y-1/2 rounded-full border-2 border-white bg-white/30 backdrop-blur" />
      </div>
    </div>
  )
}

// 使用示例
<ImageComparison
  beforeSrc="/images/product-before.webp"
  afterSrc="/images/product-after.webp"
  beforeAlt="产品修饰前"
  afterAlt="产品修饰后"
  width={600}
  height={400}
/>
```

### 图像放大镜

实现产品细节查看的放大镜效果。

```jsx
// components/custom/image-magnifier.tsx
import { useState, useRef } from "react"
import Image from "next/image"

interface ImageMagnifierProps {
  src: string
  alt: string
  width: number
  height: number
  magnifierSize?: number
  zoomLevel?: number
}

export function ImageMagnifier({
  src,
  alt,
  width,
  height,
  magnifierSize = 150,
  zoomLevel = 2.5,
}: ImageMagnifierProps) {
  const [showMagnifier, setShowMagnifier] = useState(false)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const imageRef = useRef<HTMLDivElement>(null)
  
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!imageRef.current) return
    
    const rect = imageRef.current.getBoundingClientRect()
    
    // 计算鼠标相对于图片的位置(%)
    const x = ((e.clientX - rect.left) / rect.width) * 100
    const y = ((e.clientY - rect.top) / rect.height) * 100
    
    setMousePosition({ x, y })
  }
  
  return (
    <div 
      ref={imageRef}
      className="relative overflow-hidden"
      style={{ width, height }}
      onMouseEnter={() => setShowMagnifier(true)}
      onMouseLeave={() => setShowMagnifier(false)}
      onMouseMove={handleMouseMove}
    >
      <Image
        src={src}
        alt={alt}
        fill
        className="object-cover"
      />
      
      {showMagnifier && (
        <div
          className="pointer-events-none absolute z-10 overflow-hidden rounded-full border-2 border-white shadow-lg"
          style={{
            width: magnifierSize,
            height: magnifierSize,
            top: mousePosition.y + "%",
            left: mousePosition.x + "%",
            transform: "translate(-50%, -50%)",
          }}
        >
          <div
            style={{
              position: "relative",
              width: width * zoomLevel,
              height: height * zoomLevel,
              left: -mousePosition.x * zoomLevel + magnifierSize / 2,
              top: -mousePosition.y * zoomLevel + magnifierSize / 2,
            }}
          >
            <Image
              src={src}
              alt={`${alt} zoomed`}
              fill
              className="object-cover"
            />
          </div>
        </div>
      )}
    </div>
  )
}

// 使用示例
<ImageMagnifier
  src="/images/product-detail.webp"
  alt="产品细节图"
  width={500}
  height={400}
  magnifierSize={180}
  zoomLevel={2.5}
/>
```

## 实际应用示例

### 产品图库

展示产品多角度图片的图库组件。

```jsx
// components/custom/product-gallery.tsx
import { useState } from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"

interface ProductImage {
  id: string
  src: string
  alt: string
}

interface ProductGalleryProps {
  images: ProductImage[]
}

export function ProductGallery({ images }: ProductGalleryProps) {
  const [activeImage, setActiveImage] = useState(images[0])
  
  return (
    <div className="grid gap-4">
      {/* 主图 */}
      <div className="relative aspect-square overflow-hidden rounded-lg bg-background">
        <Image
          src={activeImage.src}
          alt={activeImage.alt}
          fill
          priority
          className="object-cover transition-all duration-300"
        />
      </div>
      
      {/* 缩略图 */}
      <div className="flex space-x-2 overflow-auto">
        {images.map((image) => (
          <button
            key={image.id}
            onClick={() => setActiveImage(image)}
            className={cn(
              "relative h-20 w-20 overflow-hidden rounded-md",
              activeImage.id === image.id 
                ? "ring-2 ring-primary ring-offset-2" 
                : "ring-1 ring-border hover:ring-2 hover:ring-primary/50"
            )}
          >
            <Image
              src={image.src}
              alt={`${image.alt} 缩略图`}
              fill
              className="object-cover"
            />
          </button>
        ))}
      </div>
    </div>
  )
}

// 使用示例
const productImages = [
  { id: "1", src: "/images/product-front.webp", alt: "产品正面视图" },
  { id: "2", src: "/images/product-back.webp", alt: "产品背面视图" },
  { id: "3", src: "/images/product-side.webp", alt: "产品侧面视图" },
  { id: "4", src: "/images/product-detail.webp", alt: "产品细节视图" },
]

<ProductGallery images={productImages} />
```

### 英雄横幅组件

实现响应式的页面顶部横幅组件。

```jsx
// components/custom/hero-banner.tsx
import { ReactNode } from "react"
import { cn } from "@/lib/utils"

interface HeroBannerProps {
  backgroundImage: string
  title: string
  subtitle?: string
  children?: ReactNode
  overlay?: boolean
  className?: string
  contentPosition?: "left" | "center" | "right"
  height?: "sm" | "md" | "lg" | "full"
  priority?: boolean
}

export function HeroBanner({
  backgroundImage,
  title,
  subtitle,
  children,
  overlay = true,
  className = "",
  contentPosition = "center",
  height = "md",
  priority = true,
}: HeroBannerProps) {
  // 高度类名
  const heightClass = {
    sm: "min-h-[30vh]",
    md: "min-h-[50vh]",
    lg: "min-h-[70vh]",
    full: "min-h-screen",
  }[height]
  
  // 内容位置类名
  const positionClass = {
    left: "justify-start text-left",
    center: "justify-center text-center",
    right: "justify-end text-right",
  }[contentPosition]
  
  return (
    <div 
      className={cn(
        "relative flex w-full flex-col items-center",
        heightClass,
        className
      )}
    >
      {/* 背景图片 */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        <img
          src={backgroundImage}
          alt=""
          className="h-full w-full object-cover"
          aria-hidden="true"
          loading={priority ? "eager" : "lazy"}
        />
        
        {/* 遮罩层 */}
        {overlay && (
          <div className="absolute inset-0 bg-black bg-opacity-40" />
        )}
      </div>
      
      {/* 内容 */}
      <div className={cn(
        "relative z-10 flex h-full w-full max-w-7xl flex-col items-center px-4 py-12 text-white",
        positionClass
      )}>
        <div className="flex flex-col items-center justify-center">
          <h1 className="text-4xl font-bold sm:text-5xl md:text-6xl">{title}</h1>
          {subtitle && (
            <p className="mt-4 max-w-2xl text-lg text-white/90 sm:text-xl">
              {subtitle}
            </p>
          )}
          {children && <div className="mt-8">{children}</div>}
        </div>
      </div>
    </div>
  )
}

// 使用示例
<HeroBanner
  backgroundImage="/images/hero-background.webp"
  title="欢迎使用我们的产品"
  subtitle="探索功能强大且易用的解决方案，提升您的工作效率"
  height="lg"
  contentPosition="center"
>
  <Button size="lg" className="mr-4">
    立即开始
  </Button>
  <Button variant="outline" size="lg">
    了解更多
  </Button>
</HeroBanner>
```

## 最佳实践

### 图片使用推荐做法

1. **适当选择格式**：根据图片内容选择最合适的格式，优先使用现代格式如WebP和AVIF
2. **提供响应式图片**：为不同屏幕尺寸提供不同分辨率的图片
3. **优化文件大小**：压缩图片并移除不必要的元数据
4. **使用懒加载**：对非首屏图片使用懒加载技术
5. **提供替代文本**：为所有非装饰性图片提供有意义的替代文本
6. **使用占位符**：在图片加载过程中显示占位符或模糊预览
7. **合理设置尺寸**：显式设置图片尺寸以防止布局偏移

### 图片使用避免事项

1. **避免过大文件**：不要上传未经优化的原始图片
2. **避免图片文字**：不要在图片中包含重要文字内容，影响可访问性和SEO
3. **避免拉伸变形**：保持图片正确的宽高比
4. **避免过度锐化或压缩**：找到质量和文件大小的平衡点
5. **避免使用太多大图**：减少页面加载的图片总量
6. **避免忽略移动设备**：确保图片在移动设备上也能良好显示
7. **避免缺少回退方案**：为WebP等现代格式提供回退选项 