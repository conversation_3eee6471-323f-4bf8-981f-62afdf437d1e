# 请求响应规范

本文档规范了项目中请求工具库的使用方式和响应处理机制。

## 📚 请求工具库概述

项目中的请求工具库基于axios实现，提供了更加便捷的请求方式和统一的错误处理机制。

### 主要功能

- 请求和响应拦截
- 统一的错误处理
- 请求取消支持
- 类型安全的API定义
- 全局加载状态管理
- 统一的请求状态反馈
- 灵活的参数传递方式

## 🔧 基本使用方式

### 核心请求方法

```typescript
import { request } from '@/lib/request';

// GET 请求
const getData = async () => {
  const result = await request.get('/api/data');
  return result;
};

// 带参数的 GET 请求
const getUserById = async (id: number) => {
  // 方式一：使用params选项
  const user1 = await request.get('/api/users', { params: { id } });
  
  // 方式二：直接传递参数对象（会自动转换为params）
  const user2 = await request.get('/api/users', { id });
  
  return user1;
};

// POST 请求
const createData = async (data: any) => {
  const result = await request.post('/api/data', data);
  return result;
};

// PUT 请求
const updateData = async (id: number, data: any) => {
  const result = await request.put(`/api/data/${id}`, data);
  return result;
};

// DELETE 请求
const deleteData = async (id: number) => {
  await request.delete('/api/data', { id });
};
```

### 直接使用request函数

```typescript
// 方式一：提供完整配置参数
const response1 = await request('/api/users', {
  method: 'get',
  params: { role: 'admin' }
});

// 方式二：提供数据和方法配置
const response2 = await request('/api/users', { id: 1 }, { method: 'get' });

// 复杂配置示例
const response = await request('/api/data', null, {
  method: 'get',
  params: { id: 1 },
  timeout: 5000,
  headers: {
    'X-Custom-Header': 'custom-value'
  },
  showLoading: true,
  loadingText: '加载中...'
});
```

## 🎯 在Next.js中的使用

### 客户端组件中使用

```typescript
"use client";

import { request } from '@/lib/request';
import { useState, useEffect } from 'react';

export default function ClientComponent() {
  const [data, setData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await request.get('/api/data');
        setData(result);
      } catch (error) {
        console.error('获取数据失败:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, []);
  
  if (isLoading) return <div>加载中...</div>;
  
  return <div>{/* 渲染数据 */}</div>;
}
```

### 服务器组件中使用

```typescript
// 服务器组件
import { cookies } from 'next/headers';

export default async function ServerComponent() {
  // 使用fetch API，支持缓存和重新验证
  const response = await fetch('https://api.example.com/data', {
    headers: {
      'Authorization': `Bearer ${cookies().get('token')?.value}`
    },
    next: { revalidate: 60 } // 60秒内复用缓存数据
  });
  
  const data = await response.json();
  
  return <div>{/* 渲染数据 */}</div>;
}
```

## 📋 API定义最佳实践

### 集中管理API请求

```typescript
// services/api/userRequestApi.ts
import { request } from '@/lib/request';
import { ApiResponse } from '@/types/api';
import { User } from '@/types/models';

// 获取用户列表
export const getUsersRequest = async (): Promise<User[]> => {
  try {
    const response = await request.get<ApiResponse<User[]>>('/api/users');
    return response.data || [];
  } catch (error) {
    console.error('获取用户列表失败:', error);
    return [];
  }
};

// 创建用户
export const createUserRequest = async (userData: Partial<User>): Promise<User | null> => {
  try {
    const response = await request.post<ApiResponse<User>>('/api/users', userData);
    return response.data || null;
  } catch (error) {
    console.error('创建用户失败:', error);
    return null;
  }
};
```

## 🔄 响应处理工具

### 基本响应处理

```typescript
import { 
  processResponse, 
  processArrayResponse,
  handleApiError, 
  safeExecute 
} from '@/lib/request';

// 处理API响应，获取数据或返回null
export const getUserRequest = async (id: string): Promise<User | null> => {
  try {
    const response = await request.get<ApiResponse<User>>(`/api/users/${id}`);
    
    return processResponse(response, {
      successMessage: "获取用户信息成功",
      errorMessage: "获取用户信息失败",
      showSuccessToast: false,
      showErrorToast: true
    });
  } catch (error) {
    handleApiError(error, "获取用户信息失败", true);
    return null;
  }
};

// 处理返回数组的API响应
export const getUsersRequest = async (): Promise<User[]> => {
  try {
    const response = await request.get<ApiResponse<User[]>>('/api/users');
    
    return processArrayResponse(response, {
      errorMessage: "获取用户列表失败",
      showErrorToast: true
    });
  } catch (error) {
    handleApiError(error, "获取用户列表失败");
    return [];
  }
};
```

### 安全执行包装器

```typescript
// 使用安全执行包装器简化异步操作和错误处理
export const createUserRequest = async (userData: Partial<User>): Promise<User | null> => {
  return safeExecute(
    async () => {
      const response = await request.post<ApiResponse<User>>('/api/users', userData);
      return processResponse(response);
    },
    "创建用户失败",
    {
      showSuccessToast: true,
      successMessage: "用户创建成功",
      showErrorToast: true,
      onSuccess: (user) => {
        console.log("创建的用户ID:", user.id);
      },
      retryCount: 1
    }
  );
};
```

## 🛡️ 状态管理与反馈

### 加载状态

```typescript
// 显示加载状态
const getDataWithLoading = async () => {
  const result = await request.get('/api/data', {
    showLoading: true,
    loadingText: '加载数据中...'
  });
  return result;
};
```

### 成功反馈

```typescript
// 显示成功消息
const createDataWithSuccess = async (data: any) => {
  const result = await request.post('/api/data', data, {
    showSuccessMessage: true,
    successMessage: '创建成功！'
  });
  return result;
};
```

### 错误处理

```typescript
// 自定义错误消息
const fetchDataWithErrorHandling = async () => {
  try {
    const result = await request.get('/api/data', {
      showErrorMessage: true,
      errorMessage: '获取数据失败，请稍后重试'
    });
    return result;
  } catch (error) {
    // 错误已被自动处理，无需额外处理
    throw error;
  }
};
```

## 🔐 认证与拦截器

### 添加认证拦截器

```typescript
import { request } from '@/lib/request';

// 添加请求拦截器
request.interceptors.request.use((config) => {
  config.headers = {
    ...config.headers,
    'Authorization': `Bearer ${getToken()}`
  };
  return config;
});

// 添加响应拦截器
request.interceptors.response.use((response) => {
  return response;
}, (error) => {
  // 处理401错误，自动刷新token
  if (error.response?.status === 401) {
    // 重定向到登录页或刷新token
  }
  return Promise.reject(error);
});
```

## 🎛️ 配置选项

请求配置支持所有axios的配置项，并额外增加了以下配置：

- `skipErrorHandler`: 是否跳过错误处理
- `getResponse`: 是否获取原始响应对象
- `showLoading`: 是否显示加载状态
- `loadingText`: 加载提示文本
- `showSuccessMessage`: 是否显示成功提示
- `successMessage`: 成功提示文本
- `showErrorMessage`: 是否显示错误提示
- `errorMessage`: 错误提示文本

## 📝 类型安全

```typescript
interface User {
  id: number;
  name: string;
  email: string;
}

// 指定返回类型为User
const getUser = async (id: number): Promise<User> => {
  return request.get<User>('/api/users', { id });
};

interface CreateUserRequest {
  name: string;
  email: string;
}

// 指定请求和返回类型
const createUser = async (userData: CreateUserRequest): Promise<User> => {
  return request.post<User>('/api/users', userData);
};
```

## 🚀 最佳实践

1. **集中管理API请求**：所有API请求方法应在`services/api`目录下集中管理
2. **类型安全**：使用TypeScript类型确保请求参数和响应数据的类型安全
3. **错误处理**：每个API请求方法都应包含适当的错误处理
4. **状态反馈**：为用户提供适当的加载、成功和错误状态反馈
5. **响应处理**：使用提供的响应处理工具简化数据处理逻辑
6. **安全执行**：对于复杂的异步操作，使用safeExecute包装器
