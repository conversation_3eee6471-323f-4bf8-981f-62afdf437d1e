# 数据可视化规范

数据可视化是直观呈现数据和信息的有效方式，本文档规范了项目中图表和数据展示的设计原则、样式指南和实现方法，确保数据可视化的一致性和可用性。

## 数据可视化原则

### 1. 简洁清晰

- 去除视觉噪音，专注于数据本身
- 确保图表能够直观地传达关键信息
- 避免过度装饰和不必要的3D效果

### 2. 适当选择

- 根据数据特性和目标选择合适的图表类型
- 考虑受众的数据理解能力和期望
- 确保图表类型能够准确表达数据关系

### 3. 一致性

- 在整个应用中保持一致的视觉风格
- 使用统一的颜色方案、字体和间距
- 保持图例、标签和交互模式的一致性

## 图表类型指南

### 基础图表

#### 折线图

适用于展示连续数据的趋势变化，如时间序列数据。

```jsx
import { Line } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js"

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
)

function LineChartExample() {
  const data = {
    labels: ["1月", "2月", "3月", "4月", "5月", "6月"],
    datasets: [
      {
        label: "销售额",
        data: [12, 19, 3, 5, 2, 3],
        borderColor: "rgb(75, 192, 192)",
        backgroundColor: "rgba(75, 192, 192, 0.5)",
        tension: 0.3,
      },
    ],
  }
  
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top",
        labels: {
          font: {
            family: "var(--font-sans)",
          },
        },
      },
      title: {
        display: true,
        text: "月度销售趋势",
        font: {
          family: "var(--font-sans)",
          size: 16,
          weight: "500",
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: "rgba(0, 0, 0, 0.05)",
        },
      },
      x: {
        grid: {
          display: false,
        },
      },
    },
  }
  
  return (
    <div className="h-64 w-full">
      <Line data={data} options={options} />
    </div>
  )
}
```

#### 柱状图

适用于比较不同类别之间的数量差异。

```jsx
import { Bar } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js"

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
)

function BarChartExample() {
  const data = {
    labels: ["产品A", "产品B", "产品C", "产品D", "产品E"],
    datasets: [
      {
        label: "销售量",
        data: [120, 190, 300, 150, 200],
        backgroundColor: [
          "rgba(53, 162, 235, 0.5)",
          "rgba(53, 162, 235, 0.5)",
          "rgba(53, 162, 235, 0.5)",
          "rgba(53, 162, 235, 0.5)",
          "rgba(53, 162, 235, 0.5)",
        ],
        borderColor: [
          "rgb(53, 162, 235)",
          "rgb(53, 162, 235)",
          "rgb(53, 162, 235)",
          "rgb(53, 162, 235)",
          "rgb(53, 162, 235)",
        ],
        borderWidth: 1,
      },
    ],
  }
  
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top",
      },
      title: {
        display: true,
        text: "产品销售对比",
        font: {
          family: "var(--font-sans)",
          size: 16,
          weight: "500",
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: "rgba(0, 0, 0, 0.05)",
        },
      },
      x: {
        grid: {
          display: false,
        },
      },
    },
  }
  
  return (
    <div className="h-64 w-full">
      <Bar data={data} options={options} />
    </div>
  )
}
```

#### 饼图/环形图

适用于显示部分与整体的关系。

```jsx
import { Doughnut } from "react-chartjs-2"
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
} from "chart.js"

ChartJS.register(ArcElement, Tooltip, Legend)

function DoughnutChartExample() {
  const data = {
    labels: ["开发", "市场", "运营", "销售", "客服"],
    datasets: [
      {
        label: "预算分配",
        data: [35, 25, 15, 15, 10],
        backgroundColor: [
          "rgba(255, 99, 132, 0.7)",
          "rgba(54, 162, 235, 0.7)",
          "rgba(255, 206, 86, 0.7)",
          "rgba(75, 192, 192, 0.7)",
          "rgba(153, 102, 255, 0.7)",
        ],
        borderColor: [
          "rgb(255, 99, 132)",
          "rgb(54, 162, 235)",
          "rgb(255, 206, 86)",
          "rgb(75, 192, 192)",
          "rgb(153, 102, 255)",
        ],
        borderWidth: 1,
      },
    ],
  }
  
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "right",
        labels: {
          font: {
            family: "var(--font-sans)",
          },
        },
      },
      title: {
        display: true,
        text: "部门预算分配",
        font: {
          family: "var(--font-sans)",
          size: 16,
          weight: "500",
        },
      },
    },
    cutout: "50%",
  }
  
  return (
    <div className="h-64 w-full">
      <Doughnut data={data} options={options} />
    </div>
  )
}
```

### 高级图表

#### 组合图表

同时展示多种图表类型，如柱状图和折线图组合。

```jsx
import { Chart } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js"

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
)

function CombinedChartExample() {
  const data = {
    labels: ["1月", "2月", "3月", "4月", "5月", "6月"],
    datasets: [
      {
        type: "bar",
        label: "收入",
        data: [50, 60, 70, 80, 90, 100],
        backgroundColor: "rgba(53, 162, 235, 0.5)",
        borderColor: "rgb(53, 162, 235)",
        borderWidth: 1,
      },
      {
        type: "line",
        label: "利润",
        data: [20, 25, 30, 35, 40, 45],
        borderColor: "rgb(255, 99, 132)",
        backgroundColor: "rgba(255, 99, 132, 0.5)",
        tension: 0.3,
      },
    ],
  }
  
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top",
        labels: {
          font: {
            family: "var(--font-sans)",
          },
        },
      },
      title: {
        display: true,
        text: "收入与利润对比",
        font: {
          family: "var(--font-sans)",
          size: 16,
          weight: "500",
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: "rgba(0, 0, 0, 0.05)",
        },
      },
      x: {
        grid: {
          display: false,
        },
      },
    },
  }
  
  return (
    <div className="h-64 w-full">
      <Chart type="bar" data={data} options={options} />
    </div>
  )
}
```

#### 雷达图

适用于多维度数据的比较。

```jsx
import { Radar } from "react-chartjs-2"
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend,
} from "chart.js"

ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend
)

function RadarChartExample() {
  const data = {
    labels: ["技术", "沟通", "团队协作", "创新", "执行力", "领导力"],
    datasets: [
      {
        label: "团队A",
        data: [80, 70, 90, 85, 75, 65],
        backgroundColor: "rgba(255, 99, 132, 0.2)",
        borderColor: "rgba(255, 99, 132, 1)",
        borderWidth: 1,
      },
      {
        label: "团队B",
        data: [70, 85, 75, 80, 90, 85],
        backgroundColor: "rgba(53, 162, 235, 0.2)",
        borderColor: "rgba(53, 162, 235, 1)",
        borderWidth: 1,
      },
    ],
  }
  
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top",
        labels: {
          font: {
            family: "var(--font-sans)",
          },
        },
      },
      title: {
        display: true,
        text: "团队能力评估",
        font: {
          family: "var(--font-sans)",
          size: 16,
          weight: "500",
        },
      },
    },
    scales: {
      r: {
        angleLines: {
          display: true,
          color: "rgba(0, 0, 0, 0.05)",
        },
        grid: {
          color: "rgba(0, 0, 0, 0.05)",
        },
        pointLabels: {
          font: {
            family: "var(--font-sans)",
          },
        },
        suggestedMin: 0,
        suggestedMax: 100,
      },
    },
  }
  
  return (
    <div className="h-64 w-full">
      <Radar data={data} options={options} />
    </div>
  )
}
```

### 小型图表

#### 迷你图（Sparkline）

适用于紧凑的空间内展示趋势。

```jsx
import { Line } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
} from "chart.js"

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement
)

function SparklineExample() {
  const data = {
    labels: Array.from({ length: 20 }, (_, i) => i + 1),
    datasets: [
      {
        data: [5, 6, 8, 7, 9, 8, 7, 8, 9, 10, 11, 10, 12, 13, 12, 11, 13, 14, 15, 14],
        borderColor: "rgb(53, 162, 235)",
        backgroundColor: "rgba(53, 162, 235, 0.5)",
        borderWidth: 1.5,
        pointRadius: 0,
        tension: 0.4,
      },
    ],
  }
  
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: false,
      },
    },
    scales: {
      x: {
        display: false,
      },
      y: {
        display: false,
        min: 0,
      },
    },
  }
  
  return (
    <div className="h-8 w-24">
      <Line data={data} options={options} />
    </div>
  )
}
```

## 图表样式指南

### 色彩系统

图表应使用项目的色彩系统，保持与整体UI的一致性。

```jsx
// 图表色彩变量
const chartColors = {
  primary: "rgb(53, 162, 235)",
  primaryLight: "rgba(53, 162, 235, 0.5)",
  secondary: "rgb(255, 99, 132)",
  secondaryLight: "rgba(255, 99, 132, 0.5)",
  success: "rgb(75, 192, 192)",
  successLight: "rgba(75, 192, 192, 0.5)",
  warning: "rgb(255, 206, 86)",
  warningLight: "rgba(255, 206, 86, 0.5)",
  error: "rgb(255, 99, 132)",
  errorLight: "rgba(255, 99, 132, 0.5)",
  neutral: "rgb(156, 163, 175)",
  neutralLight: "rgba(156, 163, 175, 0.5)",
  grid: "rgba(0, 0, 0, 0.05)",
}

// 多数据集的色彩配置
const multiDatasetColors = {
  backgroundColor: [
    chartColors.primaryLight,
    chartColors.secondaryLight,
    chartColors.successLight,
    chartColors.warningLight,
    chartColors.errorLight,
    chartColors.neutralLight,
  ],
  borderColor: [
    chartColors.primary,
    chartColors.secondary,
    chartColors.success,
    chartColors.warning,
    chartColors.error,
    chartColors.neutral,
  ],
}
```

### 字体与标签

确保图表中的文字与整体UI的字体一致，使用清晰易读的标签。

```jsx
// 图表字体配置
const chartFonts = {
  title: {
    family: "var(--font-sans)",
    size: 16,
    weight: "500",
  },
  label: {
    family: "var(--font-sans)",
    size: 14,
    weight: "400",
  },
  legend: {
    family: "var(--font-sans)",
    size: 12,
    weight: "400",
  },
  tooltip: {
    family: "var(--font-sans)",
    size: 12,
    weight: "400",
  },
}

// 标签格式化示例
const formatters = {
  // 金额格式化
  currency: (value) => `¥${value.toLocaleString()}`,
  
  // 百分比格式化
  percentage: (value) => `${value}%`,
  
  // 日期格式化
  date: (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  },
}
```

### 网格与轴线

使用轻量级的网格线和轴线，减少视觉干扰。

```jsx
// 网格与轴线配置
const gridConfig = {
  x: {
    grid: {
      display: false, // 不显示X轴网格线
    },
    border: {
      display: true,
      color: "rgba(0, 0, 0, 0.1)",
    },
    ticks: {
      color: "rgba(0, 0, 0, 0.6)",
      font: chartFonts.label,
    },
  },
  y: {
    grid: {
      display: true,
      color: "rgba(0, 0, 0, 0.05)", // 浅色网格线
    },
    border: {
      display: true,
      color: "rgba(0, 0, 0, 0.1)",
    },
    ticks: {
      color: "rgba(0, 0, 0, 0.6)",
      font: chartFonts.label,
    },
  },
}
```

## 图表容器和布局

### 图表卡片

将图表封装在卡片容器中，提供一致的边距和阴影。

```jsx
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

function ChartCard({ title, subtitle, chart }) {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium">{title}</CardTitle>
        {subtitle && (
          <p className="text-sm text-muted-foreground">{subtitle}</p>
        )}
      </CardHeader>
      <CardContent>
        <div className="h-64">{chart}</div>
      </CardContent>
    </Card>
  )
}

// 使用示例
<ChartCard 
  title="月度销售趋势" 
  subtitle="过去6个月的销售数据" 
  chart={<LineChartExample />} 
/>
```

### 响应式布局

确保图表能够适应不同屏幕尺寸。

```jsx
// 响应式图表布局
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  <ChartCard 
    title="销售趋势" 
    chart={<LineChartExample />} 
  />
  <ChartCard 
    title="收入分布" 
    chart={<PieChartExample />} 
  />
  <ChartCard 
    title="用户增长" 
    chart={<BarChartExample />} 
  />
</div>

// 全宽图表
<div className="w-full">
  <ChartCard 
    title="年度业绩对比" 
    chart={<BarChartExample />} 
  />
</div>
```

## 交互与工具提示

### 基本交互

图表应提供基本的悬停和点击交互功能。

```jsx
// 工具提示配置
const tooltipConfig = {
  enabled: true,
  mode: "index",
  intersect: false,
  backgroundColor: "rgba(255, 255, 255, 0.9)",
  titleColor: "rgb(0, 0, 0)",
  bodyColor: "rgb(0, 0, 0)",
  borderColor: "rgba(0, 0, 0, 0.1)",
  borderWidth: 1,
  cornerRadius: 4,
  padding: 8,
  boxPadding: 4,
  titleFont: chartFonts.tooltip,
  bodyFont: chartFonts.tooltip,
  callbacks: {
    label: function(context) {
      let label = context.dataset.label || "";
      if (label) {
        label += ": ";
      }
      if (context.parsed.y !== null) {
        label += new Intl.NumberFormat("zh-CN", {
          style: "currency",
          currency: "CNY",
        }).format(context.parsed.y);
      }
      return label;
    }
  }
}
```

### 高级交互

为图表添加更复杂的交互功能，如缩放、平移等。

```jsx
import { Line } from "react-chartjs-2"
import { zoom } from "chartjs-plugin-zoom"
import { Chart as ChartJS, registerables } from "chart.js"

ChartJS.register(...registerables, zoom)

function ZoomableChart() {
  const data = {
    // 数据配置...
  }
  
  const options = {
    // 基本配置...
    plugins: {
      zoom: {
        pan: {
          enabled: true,
          mode: "x",
        },
        zoom: {
          wheel: {
            enabled: true,
          },
          pinch: {
            enabled: true,
          },
          mode: "x",
        },
      },
    },
  }
  
  return (
    <div className="h-64 w-full">
      <Line data={data} options={options} />
    </div>
  )
}
```

## 图表加载和错误状态

### 加载状态

当图表数据正在加载时显示适当的加载状态。

```jsx
import { Skeleton } from "@/components/ui/skeleton"

function ChartSkeleton() {
  return (
    <div className="space-y-2">
      <Skeleton className="h-8 w-1/3" />
      <Skeleton className="h-64 w-full" />
    </div>
  )
}

// 使用示例
function ChartWithLoading({ isLoading, data }) {
  if (isLoading) {
    return <ChartSkeleton />
  }
  
  return <BarChart data={data} />
}
```

### 错误状态

当图表数据加载失败时显示友好的错误信息。

```jsx
function ChartError({ message = "无法加载图表数据" }) {
  return (
    <div className="flex items-center justify-center h-64 w-full border border-dashed rounded-md">
      <div className="text-center">
        <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2" />
        <p className="text-muted-foreground">{message}</p>
        <Button variant="outline" size="sm" className="mt-4">
          重试
        </Button>
      </div>
    </div>
  )
}

// 使用示例
function ChartWithErrorHandling({ isLoading, error, data }) {
  if (isLoading) {
    return <ChartSkeleton />
  }
  
  if (error) {
    return <ChartError message={error.message} />
  }
  
  return <BarChart data={data} />
}
```

## 数据看板

### 基本看板布局

创建数据看板，展示多个相关图表。

```jsx
function Dashboard() {
  return (
    <div className="space-y-8">
      {/* 概览数据卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">总销售额</p>
                <p className="text-2xl font-bold mt-1">¥128,430</p>
                <p className="text-xs text-success-600 flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" /> 
                  增长15.3%
                </p>
              </div>
              <div className="h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* 更多数据卡片... */}
      </div>
      
      {/* 主要图表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartCard 
          title="销售趋势" 
          subtitle="过去12个月" 
          chart={<LineChartExample />} 
        />
        <ChartCard 
          title="收入来源" 
          subtitle="按产品类别" 
          chart={<DoughnutChartExample />} 
        />
      </div>
      
      {/* 详细数据图表 */}
      <ChartCard 
        title="区域销售对比" 
        subtitle="按季度分析" 
        chart={<BarChartExample />} 
      />
    </div>
  )
}
```

### 交互式看板

创建具有筛选和交互功能的数据看板。

```jsx
function InteractiveDashboard() {
  const [period, setPeriod] = useState("monthly")
  const [category, setCategory] = useState("all")
  
  // 基于筛选条件获取数据
  const data = useMemo(() => {
    // 根据period和category处理数据
    return getFilteredData(period, category)
  }, [period, category])
  
  return (
    <div className="space-y-6">
      {/* 筛选控件 */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="w-full sm:w-48">
          <Label htmlFor="period">时间周期</Label>
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger id="period">
              <SelectValue placeholder="选择周期" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">日</SelectItem>
              <SelectItem value="weekly">周</SelectItem>
              <SelectItem value="monthly">月</SelectItem>
              <SelectItem value="quarterly">季度</SelectItem>
              <SelectItem value="yearly">年</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="w-full sm:w-48">
          <Label htmlFor="category">产品类别</Label>
          <Select value={category} onValueChange={setCategory}>
            <SelectTrigger id="category">
              <SelectValue placeholder="选择类别" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部</SelectItem>
              <SelectItem value="electronics">电子产品</SelectItem>
              <SelectItem value="clothing">服装</SelectItem>
              <SelectItem value="food">食品</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* 图表内容 */}
      <ChartCard 
        title={`${getCategoryName(category)}销售趋势`}
        subtitle={`按${getPeriodName(period)}统计`}
        chart={<LineChartExample data={data.trendsData} />}
      />
      
      {/* 更多图表... */}
    </div>
  )
}
```

## 最佳实践

### 数据可视化推荐做法

1. **选择合适的图表类型**：根据数据关系和展示目标选择合适的图表类型
2. **保持简洁**：删除不必要的元素，专注于数据本身
3. **提供上下文**：通过标题、副标题和注释提供必要的上下文信息
4. **一致的色彩系统**：使用一致的色彩方案，确保可访问性
5. **适当的比例**：确保图表比例不会误导读者

### 数据可视化避免事项

1. **避免3D效果**：3D效果通常会扭曲数据感知，应避免使用
2. **避免过度装饰**：不要使用过多的视觉元素分散对数据的注意力
3. **避免过多数据**：单个图表中避免显示过多数据点，必要时分组或筛选
4. **避免不合适的类型**：避免使用不适合数据关系的图表类型
5. **避免彩虹色彩**：避免使用过多不同的颜色，这会增加认知负担

## 实际应用示例

### 销售仪表盘

```jsx
function SalesDashboard() {
  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <StatCard 
          icon={DollarSign}
          title="总销售额"
          value="¥128,430"
          trend="up"
          change="+15.3%"
        />
        <StatCard 
          icon={Users}
          title="新客户"
          value="8,492"
          trend="up"
          change="+5.2%"
        />
        <StatCard 
          icon={ShoppingCart}
          title="订单数"
          value="12,345"
          trend="up"
          change="+12.1%"
        />
        <StatCard 
          icon={TrendingUp}
          title="平均订单额"
          value="¥256"
          trend="down"
          change="-2.3%"
        />
      </div>
      
      <ChartCard 
        title="月度销售与目标对比" 
        subtitle="2023年" 
        chart={
          <CombinedChartExample 
            data={{
              labels: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
              actualData: [5000, 7500, 10000, 8000, 12000, 15000, 18000, 17000, 20000, 18000, 22000, 25000],
              targetData: [8000, 8500, 9000, 9500, 10000, 12000, 14000, 16000, 18000, 20000, 22000, 24000]
            }}
          />
        } 
      />
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartCard 
          title="销售渠道分布" 
          chart={
            <DoughnutChartExample 
              data={{
                labels: ["直营店", "电商平台", "代理商", "社交媒体", "其他"],
                values: [35, 25, 20, 15, 5]
              }}
            />
          } 
        />
        <ChartCard 
          title="Top 5 畅销产品" 
          chart={
            <BarChartExample 
              data={{
                labels: ["产品A", "产品B", "产品C", "产品D", "产品E"],
                values: [250, 220, 180, 150, 120]
              }}
              horizontal={true}
            />
          } 
        />
      </div>
    </div>
  )
}
```

### 用户活动分析

```jsx
function UserActivityDashboard() {
  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <StatCard 
          icon={Users}
          title="活跃用户"
          value="15,234"
          trend="up"
          change="+8.3%"
        />
        <StatCard 
          icon={Clock}
          title="平均使用时长"
          value="24分钟"
          trend="up"
          change="+2.1%"
        />
        <StatCard 
          icon={BarChart2}
          title="转化率"
          value="3.2%"
          trend="down"
          change="-0.5%"
        />
      </div>
      
      <ChartCard 
        title="用户增长趋势" 
        chart={
          <LineChartExample 
            data={{
              labels: ["1月", "2月", "3月", "4月", "5月", "6月"],
              datasets: [
                {
                  label: "新用户",
                  data: [500, 800, 1200, 1500, 2000, 2500],
                  borderColor: chartColors.primary,
                  backgroundColor: chartColors.primaryLight,
                },
                {
                  label: "活跃用户",
                  data: [1500, 1700, 2000, 2200, 2700, 3200],
                  borderColor: chartColors.success,
                  backgroundColor: chartColors.successLight,
                }
              ]
            }}
          />
        } 
      />
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartCard 
          title="用户地区分布" 
          chart={
            <PieChartExample 
              data={{
                labels: ["华东", "华南", "华北", "西南", "东北", "西北"],
                values: [30, 25, 20, 15, 5, 5]
              }}
            />
          } 
        />
        <ChartCard 
          title="设备使用情况" 
          chart={
            <BarChartExample 
              data={{
                labels: ["iOS", "Android", "Web", "其他"],
                values: [45, 40, 12, 3]
              }}
            />
          } 
        />
      </div>
    </div>
  )
}
``` 